import yaml
import json
import os
import argparse
import copy
import sys
from os.path import abspath
import math

parser = argparse.ArgumentParser(
    description='Api Generator', allow_abbrev=True)
parser.add_argument('--api-file-path', type=str,
                    help='api文件路径')
parser.add_argument('--api-dir-path', type=str,
                    help='api文件夹路径')
args, _ = parser.parse_known_args()

import random
import sys

MIN_INT = -sys.maxsize -1
MAX_INT = sys.maxsize
MIN_LONG = -9223372036854775808
MAX_LONG = 9223372036854775807

class FuzzyBuilder(object):
    """
    模糊生成类
    """
    def __init__(self):
        self.blocks_ = [chr(i) for i in range(0,16)]
        self.emptys_ = [chr(i) for i in range(16,33)]
        self.nums_ = [chr(i) for i in range(48,58)]
        self.lower_case_letters_ = [chr(i) for i in range(97,123)]
        self.upper_case_letters_ = [chr(i) for i in range(65,91)]
        self.symbols_ = [chr(i) for i in range(33,48)] + [chr(i) for i in range(58,65)] + [chr(i) for i in range(91,97)] + [chr(i) for i in range(123,128)]
        pass

    def getRandomString(self, bits = 30, *, contain_blocks = False, contain_emptys = False, contain_nums = True, contain_lower_case_letters = True, contain_upper_case_ltters = True, contain_symbols = False):
        """

        `bits` : 需要生成的string长度<int>

        `contain_blocks` : 总集合是否包含blocks, ASCII 0,...,15 <bool>

        `contain_emptys` : 总集合是否包含emptys, ASCII 16,...,32 <bool>

        `contain_nums` : 总集合是否包含数字, ASCII 48,..,57 <bool>

        `contain_lower_case_letters` : 总集合是否包含小写字母, ASCII 97,...,122 <bool>

        `contain_upper_case_ltters` : 总集合是否包含大写字母, ASCII 65,...,90 <bool>

        `contain_symbols` : 总集合是否包含符号, ASCII 33,...,47 + 58,...,64 + 91,...,96 + 123,...,127 <bool>
        """
        all_characters = []
        if contain_blocks:
            all_characters += self.blocks_
        if contain_emptys:
            all_characters += self.emptys_
        if contain_nums:
            all_characters += self.nums_
        if contain_lower_case_letters:
            all_characters += self.lower_case_letters_
        if contain_upper_case_ltters:
            all_characters += self.upper_case_letters_
        if contain_symbols:
            all_characters += self.symbols_
        result = "".join(random.sample(all_characters, bits))
        return result

    def getRandomInt(self, *, min = MIN_INT, max = MAX_INT):
        return random.randint(min, max)

    def getRandomLong(self, *, min = MIN_LONG, max = MAX_LONG):
        return random.randint(min, max)

    def getRandomFloat(self, *, min = sys.float_info.min, max = sys.float_info.max):
        return random.uniform(min, max)

    def getRandomBool(self):
        return bool(random.getrandbits(1))

fuzzyBuilder = FuzzyBuilder()


class JsonTool:
    """json解析出叶子节点工具类

    """
    def __init__(self):
        """构造函数

        """
        self.full_json_path_list = []
        self.source_json = {}
        pass

    def set_source_json(self, source_json):
        """设置需要解析的json object

        `source_json`: 需要解析的json object <dict>

        """
        self.source_json = source_json
        pass

    def traversal_json(self):
        """遍历json叶子节点

        """
        self.traversal_json_imp(self.source_json, [])
        pass

    def traversal_json_imp(self, source_json, parent_json_path):
        """遍历json叶子节点内部实现

        """
        for key in source_json.keys():
            if isinstance(source_json[key], dict):
                temp_parent_json_path = copy.deepcopy(parent_json_path)
                temp_parent_json_path.append(key)
                self.traversal_json_imp(source_json[key], temp_parent_json_path)

            elif isinstance(source_json[key], list):
                temp_parent_json_path = copy.deepcopy(parent_json_path)
                temp_parent_json_path.append(key)
                index_key = 0
                for value in source_json[key]:
                    temp_path = copy.deepcopy(temp_parent_json_path)
                    temp_path.append(index_key)
                    index_key += 1
                    if isinstance(value, dict) or isinstance(value, list):
                        self.traversal_json_imp(value, temp_path)
                    else:
                        full_json_path = temp_path
                        self.full_json_path_list.append(full_json_path)
            else:
                full_json_path = copy.deepcopy(parent_json_path)
                full_json_path.append(key)
                self.full_json_path_list.append(full_json_path)
            pass
class TestCasesYamlTool(object):
    """用来生成testcase文件

    """

    def __init__(self):
        self.testcase_yaml_obj = {}
        pass

    def generateTestcases(self, dir_name, file_name, variable, length):
        output_yaml_file_path = os.path.join(abspath("testcases/%s" % (dir_name)), file_name)
        create_file(output_yaml_file_path)
        # with open(output_yaml_file_path, "r", encoding='utf-8') as yaml_file:
        #     self.testcase_yaml_obj = yaml.load(yaml_file)
        file_name_without_ext = file_name.split('.')[0]
        self.testcase_yaml_obj = [{'config': {'name': '%s' % (file_name_without_ext)}}]
        variable_value = None
        section_length = math.ceil((length - 2) / 3.0)
        for i in range(0,length):
            if i == 0:
                variable_value = False
            elif i == 1:
                variable_value = True
            elif math.floor((i - 2) / section_length) == 0:
                variable_value = fuzzyBuilder.getRandomString()
            elif math.floor((i - 2) / section_length) == 1:
                variable_value = fuzzyBuilder.getRandomInt()
            elif math.floor((i - 2) / section_length) == 2:
                variable_value = fuzzyBuilder.getRandomFloat()
            test_root_object = {}
            test_value_object = {}
            test_value_object["name"] = "%s_%s" % (file_name_without_ext, variable_value)
            test_value_object["api"] = "api\\%s\\%s" % (dir_name, file_name)
            test_variables_value = []
            test_variable_value = {}
            test_variable_value[variable] = variable_value
            test_variables_value.append(test_variable_value)
            test_value_object["variables"] = test_variables_value
            test_root_object["test"] = test_value_object
            self.testcase_yaml_obj.append(test_root_object)

        self.flush(self.testcase_yaml_obj, output_yaml_file_path)
        pass

    def flush(self, yaml_obj, output_file):
        """打开api yaml文件

        `yaml_obj`: 解析过的yaml object <dict>

        `output_file`: api yaml文件路径 <str>

        """
        with open(output_file, "w", encoding='utf-8') as yaml_file:
            yaml.dump(yaml_obj, yaml_file)
        pass

class ApiYamlTool(object):
    """用来生成api 的模糊测试文件

    """

    def __init__(self):
        """构造函数

        """
        self.api_yaml_obj  = None
        self.api_yaml_path = None
        self.api_yaml_dir = ""
        self.api_yaml_whole_name = ""
        self.api_yaml_name = ""
        self.api_yaml_ext = ""
        self.json_tool = JsonTool()
        self.testcase_tool = TestCasesYamlTool()
        pass

    def openYaml(self, yaml_path):
        """打开api yaml文件

        `yaml_path`: api yaml文件路径 <str>

        """
        self.api_yaml_path = yaml_path
        self.api_yaml_dir = os.path.dirname(yaml_path)
        self.api_yaml_whole_name = os.path.split(yaml_path)[-1]
        self.api_yaml_name = self.api_yaml_whole_name.split('.')[0]
        self.api_yaml_ext = os.path.splitext(yaml_path)[1]
        with open(yaml_path, "r", encoding='utf-8') as yaml_file:
            self.api_yaml_obj = yaml.load(yaml_file)
        pass

    def generateFuzzyApiYaml(self):
        """生成fuzzy api yaml，调用之前先调用openYaml

        """
        api_method = self.api_yaml_obj["request"]["method"]
        if api_method=="GET":
            pass
        else:
            yaml_json_obj = self.api_yaml_obj["request"]["json"]
            self.json_tool.set_source_json(yaml_json_obj)
            self.json_tool.traversal_json()
            for full_json_path in self.json_tool.full_json_path_list:
                str_full_json_path = ""
                temp_api_yaml_obj = copy.deepcopy(self.api_yaml_obj)
                temp_json = temp_api_yaml_obj["request"]["json"]
                for path in full_json_path:
                    if str_full_json_path.strip()=="":
                        str_full_json_path = path
                    else:
                        str_full_json_path = "%s_%s" % (str_full_json_path, path)
                    if isinstance(temp_json[path], dict) or isinstance(temp_json[path], list):
                        temp_json = temp_json[path]
                    else:
                        temp_json[path] = "%s%s" % ("$", str_full_json_path)
                output_yaml_name = "%s_%s%s" % (self.api_yaml_name, str_full_json_path, self.api_yaml_ext)
                output_yaml_file_path = os.path.join(self.api_yaml_dir, output_yaml_name)
                create_file(output_yaml_file_path)
                relative_dir_name = os.path.relpath(self.api_yaml_dir, abspath("api"))
                variable = str_full_json_path
                case_count = 50
                self.testcase_tool.generateTestcases(relative_dir_name, output_yaml_name, variable, case_count)
                self.flush(temp_api_yaml_obj, output_yaml_file_path)
                print(full_json_path)
            pass

    def flush(self, yaml_obj, output_file):
        """打开api yaml文件

        `yaml_obj`: 解析过的yaml object <dict>

        `output_file`: api yaml文件路径 <str>

        """
        with open(output_file, "w", encoding='utf-8') as yaml_file:
            yaml.dump(yaml_obj, yaml_file)
        pass

def create_file(filename):

    """创建文件

        `filename`: 需要创建的文件路径 <str>

     """
    if not os.path.isdir(os.path.dirname(filename)):
        os.makedirs(os.path.dirname(filename))
    if not os.path.isfile(filename):
        fd = open(filename, mode="w", encoding="utf-8")
        fd.close()
    else:
        pass

def handleApiYamlFile(source_api_yaml_path):
    """处理api yaml 文件

        `source_api_yaml_path`: api的yaml文件路径 <str>

     """
    api_yaml_tool = ApiYamlTool()
    api_yaml_tool.openYaml(source_api_yaml_path)
    api_yaml_tool.generateFuzzyApiYaml()
    pass


def filterYamlFileList(dir_path):
    file_list = os.listdir(dir_path)
    result_list = []
    for file in file_list:
        if os.path.splitext(file)[1] == ".yml" or os.path.splitext(file)[1] == ".yaml":
            result_list.append(file)
    return result_list

if __name__ == '__main__' :
    if args.api_file_path != None:
        handleApiYamlFile(abspath(args.api_file_path))
        pass
    elif args.api_dir_path != None:
        api_file_list = filterYamlFileList(abspath(args.api_dir_path))
        for api_file in api_file_list:
            handleApiYamlFile(os.path.join(abspath(args.api_dir_path), api_file))
        pass