- config:
    name: 发起流程
    base_url: ${ENV(contract_manager_url)}
    variables:
        - code: 0
        - message: 成功
        - name: tasktest-批量发起-${getTimeStamp()}



- test:
      name: 发起流程-模板批量发起流程-跳过填写
      variables:
        flowTemplateId: "d42e98775b1941da943a0cad91eb47e3"
        scene: 2
        participantSubjectType1: 1
        role1: "1"
        sealType1: "0,1"
        roleSet1: 1
        fillOrder1: 1
        signOrder1: 1
        participantId1: "c04b8d0d854f482ab65b5481b9a497cc"
        account1: "***********"
        accountOid1: ${ENV(ouid)}
        accountName1: "宁庶升"
        subjectId1: "028152b557294a3588e7a8378d6b46ee"
        subjectName1: "esigntest青莲世界五百强"
        subjectType1: 1
        participantSubjectType2: 0
        role2: "3"
        sealType2: "0,1"
        roleSet2: 1
        fillOrder2: 2
        signOrder2: 1
        participantId2: "8f7a7a33e4d643f3a8ccddffc64bb178"
        account2: "***********"
        accountOid2: ${ENV(ouid)}
        accountName2: "宁庶升"
        subjectId2: ${ENV(ouid)}
        subjectName2: "宁庶升"
        subjectType2: 0
        account3: "***********"
        accountOid3: ${ENV(ouid2)}
        accountName3: "武玉华"
        subjectId3: ${ENV(ouid2)}
        subjectName3: "武玉华"
        subjectType3: 0
        initiatorAccountId: ${ENV(ouid)}
        taskName: $name
        platform: 5
        skipFill: true
        tenantId: "028152b557294a3588e7a8378d6b46ee"

      api: api/contract_process/processStart.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

- test:
      name: 发起流程-模板批量发起流程-不跳过填写
      variables:
        flowTemplateId: "d42e98775b1941da943a0cad91eb47e3"
        scene: 2
        participantSubjectType1: 1
        role1: "1"
        sealType1: "0,1"
        roleSet1: 1
        fillOrder1: 1
        signOrder1: 1
        participantId1: "c04b8d0d854f482ab65b5481b9a497cc"
        account1: "***********"
        accountOid1: ${ENV(ouid)}
        accountName1: "宁庶升"
        subjectId1: "028152b557294a3588e7a8378d6b46ee"
        subjectName1: "esigntest青莲世界五百强"
        subjectType1: 1
        participantSubjectType2: 0
        role2: "3"
        sealType2: "0,1"
        roleSet2: 1
        fillOrder2: 2
        signOrder2: 1
        participantId2: "8f7a7a33e4d643f3a8ccddffc64bb178"
        account2: "***********"
        accountOid2: ${ENV(ouid)}
        accountName2: "宁庶升"
        subjectId2: ${ENV(ouid)}
        subjectName2: "宁庶升"
        subjectType2: 0
        account3: "***********"
        accountOid3: ${ENV(ouid2)}
        accountName3: "武玉华"
        subjectId3: ${ENV(ouid2)}
        subjectName3: "武玉华"
        subjectType3: 0
        initiatorAccountId: ${ENV(ouid)}
        taskName: $name
        platform: 5
        skipFill: false
        tenantId: "028152b557294a3588e7a8378d6b46ee"

      api: api/contract_process/processStart.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

- test:
      name: 发起流程-直接发起
      variables:
        scene: 1
        participantSubjectType1: 0
        role1: "3"
        sealType1: "0,1"
        roleSet1: 1
        fillOrder1: 0
        signOrder1: 1
        participantId1: null
        account1: "***********"
        accountOid1: ${ENV(ouid)}
        accountName1: "宁庶升"
        subjectId1: ${ENV(ouid)}
        subjectName1: "宁庶升"
        subjectType1: 0
        account2: "***********"
        accountOid2: ${ENV(ouid2)}
        accountName2: "武玉华"
        subjectId2: ${ENV(ouid2)}
        subjectName2: "武玉华"
        subjectType2: 0
        initiatorAccountId: ${ENV(ouid)}
        taskName: $name
        platform: 5
        skipFill: false
        tenantId: "028152b557294a3588e7a8378d6b46ee"

      api: api/contract_process/processStart_local.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
