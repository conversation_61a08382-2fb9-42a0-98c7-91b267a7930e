#标准签兼容了这个字solution字段，目前可以为空，注意点：以后做了默认解决方案如何去判断该问题
- config:
    name: 轩辕saas(标准签发起)
    base_url: ${ENV(contract_manager_url)}


- test:
    name: 校验发起的接口
    api: api/contract_process/startProcess_XYSaas.yml
    variables:
      code: 0
      message: 成功
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: cc_account传空
    api: api/contract_process/startProcess_XYSaas.yml
    variables:
      cc_account: null
      code: 0
      message: 成功
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: initiatorAccountId发起人账号为空
    api: api/contract_process/startProcess_XYSaas.yml
    variables:
      initiatorAccountId: null
      code: ********
      message:  "参数错误: initiatorAccountId不能为空"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]



- test:
    name: initiatorAccountId发起人账号不存在
    api: api/contract_process/startProcess_XYSaas.yml
    variables:
      initiatorAccountId: "4327587436543598435"
      code: *********
      message: 账号不存在或已注销
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: paltform 传5
    api: api/contract_process/startProcess_XYSaas.yml
    variables:
      paltform: 5
      code: 0
      message: 成功
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: paltform不传
    api: api/contract_process/startProcess_XYSaas.yml
    variables:
      paltform: null
      code: 0
      message: 成功
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: paltform为1
    api: api/contract_process/startProcess_XYSaas.yml
    variables:
      paltform: 1
      code: 0
      message: 成功
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: paltform为3
    api: api/contract_process/startProcess_XYSaas.yml
    variables:
      paltform: 3
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]




- test:
    name: participantId 为空,文件发起，传空正常
    api: api/contract_process/startProcess_XYSaas.yml
    variables:
      participantId: null
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: participantId 不存在,直接发起，该字段不校验
    api: api/contract_process/startProcess_XYSaas.yml
    variables:
      participantId: 321432143214
      code: 0
      message: "成功"

    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: taskName 字段长度大于100
    api: api/contract_process/startProcess_XYSaas.yml
    variables:
      taskName: ${generate_random_str(200)}
      code: ********
      message: "参数错误: taskName最多50个字符" #原错误码：cooperationName长度最高为100
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

#备注：这两个字段不传都不会报错，确保是什么逻辑，轩辕saas不会报错，轩辕流程api会报错
#- test:
#    name: accountName为空
#    api: api/contract_process/startProcess_XYSaas.yml
#    variables:
#      accountName: null
#      code: ********
#      message: "参数错误: accountName不能为空"
#    validate:
#    - eq: [content.code, $code]
#    - eq: [content.message, $message]
#
#
#- test:
#    name: account为空
#    api: api/contract_process/startProcess_XYSaas.yml
#    variables:
#      account: null
#      code: ********
#      message: "参数错误: account不能为空"
#    validate:
#    - eq: [content.code, $code]
#    - eq: [content.message, $message]

- test:
    name: subjectName为空
    api: api/contract_process/startProcess_XYSaas.yml
    variables:
      subjectName: null
      code: ********
      message: "参数错误: subjectName不能为空"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

#- test:
#    name: subjectType为空(轩辕saas接口层次可以为空，确认是否增加校验)
#    api: api/contract_process/startProcess_XYSaas.yml
#    variables:
#      subjectType: null
#      code: ********
#      message: "参数错误: subjectType不能为空"
#    validate:
#    - eq: [content.code, $code]
#    - eq: [content.message, $message]


- test:
    name: solutionNum标准签传该字段传null
    api: api/contract_process/startProcess_XYSaas.yml
    variables:
      solutionNum: null
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: solutionNum标准签传
    api: api/contract_process/startProcess_XYSaas.yml
    variables:
      solutionNum: CJ0FsiZo5u
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: sealType传0
    api: api/contract_process/startProcess_XYSaas.yml
    variables:
      sealType: 0
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]




