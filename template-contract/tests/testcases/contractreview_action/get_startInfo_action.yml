- config:
    name: 获取发起流程数据-参数校验
    base_url: ${ENV(contract_manager_url)}


- test:
    name: 获取发起流程数据-bizId为空
    api: api/contract_review/get_startInfo.yml
    variables:
      - bizId: ""
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]


- test:
    name: 获取发起流程数据-bizId不存在
    api: api/contract_review/get_startInfo.yml
    variables:
      - bizId: 123
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]


- test:
    name: 获取发起流程数据-tenantId不存在
    api: api/contract_review/get_startInfo.yml
    variables:
      - tenantId: "123"
      - bizId: 123
    validate:
      - eq: ["content.code", 312030000]
      - contains: ["content.message", 账号不存在或已注销]
