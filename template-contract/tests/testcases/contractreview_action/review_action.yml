- config:
    name: 审阅合同文件-参数校验
    base_url: ${ENV(contract_manager_url)}



- test:
    name: 审阅合同文件-tenantId不存在
    api: api/contract_review/review.yml
    variables:
      - tenantId: "123"
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", 账号不存在或已注销]


- test:
    name: 审阅合同文件-accountId为空
    api: api/contract_review/review.yml
    variables:
      - accountId: ""
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 当前操作人不能为空]


- test:
    name: 审阅合同文件-fileId为空
    api: api/contract_review/review.yml
    variables:
      - fileId: ""
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 文件Id不能为空]


- test:
    name: 审阅合同文件-fileId与租户不匹配
    api: api/contract_review/review.yml
    variables:
      - fileId: 1e68e0e451ec46488a2059642f43d9a8
    validate:
      - eq: ["content.code", *********]
      - contains: ["content.message", 文件拥有者不匹配]


- test:
    name: 审阅合同文件-fileId为非word格式的文件
    api: api/contract_review/review.yml
    variables:
      - fileId: 7ed1d251964b46c9a06e76e2fea9dd2a
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 文件必须是word格式]


- test:
    name: 审阅合同文件-docElements为空
    api: api/contract_review/review.yml
    variables:
      - docElements: []
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 文档元素据不能为空]


- test:
    name: 审阅合同文件-classType为空
    api: api/contract_review/review.yml
    variables:
      - docElements: [{"classType": "","index": 0,"text": ""}]
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 类型不能为空]


- test:
    name: 审阅合同文件-index非数字
    api: api/contract_review/review.yml
    variables:
      - docElements: [{"classType": "test","index": "test测试","text": ""}]
    validate:
      - eq: [status_code, 400]


- test:
    name: 审阅合同文件-index小于0
    api: api/contract_review/review.yml
    variables:
      - docElements: [{"classType": "test","index": -1,"text": ""}]
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", 段落下标值不能小于0]


- test:
    name: 审阅合同文件-租户为个人，accountId与租户Id不一致
    api: api/contract_review/review.yml
    variables:
      - tenantId: ${ENV(gray_user_oid)}
      - accountId: 123
    validate:
      - eq: ["content.code", ********]
      - contains: ["content.message", accountId与租户不一致]
