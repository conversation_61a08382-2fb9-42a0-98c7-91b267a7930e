- config:
    name: 合同审阅场景测试
    base_url: ${ENV(contract_manager_url)}



- test:
    name: 审阅合同文件-非经销代理合同
    api: api/contract_review/review.yml
    variables:
      - fileId: ${ENV(fileId_review_org)}
      - docElements: [{"classType": "test","index": 0,"text": ""}]
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.needReview", false]


- test:
    name: 审阅合同文件-个人空间下，经销代理合同
    api: api/contract_review/review.yml
    variables:
      - tenantId: ${ENV(gray_user_oid)}
      - fileId: ${ENV(fileId_review_jx)}
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.needReview", true]


- test:
    name: 审阅合同文件-企业空间下，经销代理合同
    api: api/contract_review/review.yml
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.needReview", true]


- test:
    name: 保存发起流程数据
    api: api/contract_review/save_startInfo.yml
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
    extract:
      - bizId: content.data


- test:
    name: 获取发起流程数据
    api: api/contract_review/get_startInfo.yml
    variables:
      - bizId: $bizId
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
      - eq: ["content.data.files.0.fileId", "${ENV(fileId_review_jx_org)}"]
