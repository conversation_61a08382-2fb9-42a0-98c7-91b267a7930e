- config:
    name: 保存发起流程数据-参数校验
    base_url: ${ENV(contract_manager_url)}


- test:
    name: 保存发起流程数据-fileIds为空
    api: api/contract_review/save_startInfo.yml
    variables:
      - fileIds: []
    validate:
      - eq: ["content.code", 31202000]
      - contains: ["content.message", 文件id列表不能为空]


- test:
    name: 保存发起流程数据-fileId为空
    api: api/contract_review/save_startInfo.yml
    variables:
      - fileIds: [""]
    validate:
      - eq: ["content.code", 31202000]
      - contains: ["content.message", 文件id不能为空]


- test:
    name: 保存发起流程数据-fileId与租户不匹配
    api: api/contract_review/save_startInfo.yml
    variables:
      - fileIds: ["1e68e0e451ec46488a2059642f43d9a8"]
    validate:
      - eq: ["content.code", 312060001]
      - contains: ["content.message", 文件拥有者不匹配]


- test:
    name: 保存发起流程数据-tenantId不存在
    api: api/contract_review/save_startInfo.yml
    variables:
      - tenantId: "123"
    validate:
      - eq: ["content.code", 312030000]
      - contains: ["content.message", 账号不存在或已注销]
