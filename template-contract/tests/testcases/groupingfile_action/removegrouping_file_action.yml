- config:
    name: 移出分类(场景测试)
    base_url: ${ENV(contract_manager_url)}

- test:
    name: 创建归档一级目录
    api: api/groupingdir/creategrouping_menus.yml
    teardown_hooks:
        - ${teardown_hook_sleep_N_secs(1)}
    extract:
        - menuId: content.data
    validate:
        - eq: ["content.code", $code1]
        - eq: ["content.message", $message1]

- test:
    name: 合同归档
    api: api/groupingfile/grouping_files.yml
    teardown_hooks:
        - ${teardown_hook_sleep_N_secs(1)}
    validate:
        - eq: ["content.code", $code2]
        - eq: ["content.message", $message2]

- test:
    name: 移出分类
    api: api/groupingfile/removegrouping_files.yml
    teardown_hooks:
        - ${teardown_hook_sleep_N_secs(1)}
    validate:
        - eq: ["content.code", $code3]
        - eq: ["content.message", $message3]

- test:
    name: 删除归档目录
    api: api/groupingdir/deletegrouping_menus.yml
    validate:
        - eq: ["content.code", $code4]
        - eq: ["content.message", $message4]
