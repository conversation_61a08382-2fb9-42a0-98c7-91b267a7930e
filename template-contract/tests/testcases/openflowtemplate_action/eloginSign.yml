                                                                                                                                                                                                                       - config:
    name: 获取流程模板操作地址


- test:
    name:  获取密文(个人用户)
    variables:
      userId: sytest001
    api: api/footstone_user_api/encryptByPublicKey.yml
    extract:
    - encryptContent1: content.data

- test:
    name:  换取免登短链
    variables:
      encryptContent: $encryptContent1
      resultUrl1: resultUrl2
    api: api/footstone_user_api/eloginSign.yml