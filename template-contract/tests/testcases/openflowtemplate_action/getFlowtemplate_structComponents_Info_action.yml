- config:
    name: 获取流程模板控件信息
    base_url: ${ENV(xy_open_url)}

#- test:
#    name:  获取流程模板信息
#    api: api/open_flowtemplate/getFlowtemplateInfo.yml
#    teardown_hooks:
#        - ${teardown_hook_sleep_N_secs(1)}
##    extract:
##        - participantId: content.data.0.participantId
#
#    validate:
#       - eq: ["content.code", $code1]
#       - eq: ["content.message", $message1]


- test:
    name: 获取流程模板信息接口参数校验(个人)
    api: api/open_flowtemplate/getFlowtemplateStructComponentsInfo.yml

    validate:
        - eq: ["content.code", $code1]
        - eq: ["content.message", $message1]
