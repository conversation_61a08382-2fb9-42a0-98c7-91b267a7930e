- config:
    name: 获取流程模板操作地址
#    base_url: ${ENV(contract_manager_url)}

- test:
      name: 文件直传创建文件
      variables:
      - json: {}
      api: api/open_flowtemplate/getUploadUrl.yml
      extract:
      - fileId: content.data.fileId
      - uploadUrl: content.data.uploadUrl
#    teardown_hooks:
#      - ${hook_sleep_n_secs(5)}


- test:
      name: 上传文件到oss
      variables:
          uploadUrl: $uploadUrl
          contentMd5: m49PzxWxAG8sMwNkZrlJDw==


      api: api/files/upload_oss.yml
      validate:
      - eq: ["content.errCode", 0]
      - eq: ["content.msg", "成功"]
      teardown_hooks:
      - ${hook_sleep_n_secs(5)}

- test:
    name: sealType2 企业印章类型不传(非指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
        sealType2: []
        code: 0
        message: "成功"
    validate:
      - eq: [content.code, $code]
      - eq: [content.message, $message]

    extract:
    - processId1: content.data.processId
#    - resultUrl1: content.data.resultUrl

- test:
    name: 直接发起,获取A个人的流程操作地址
    api: api/open_flowtemplate/getProcessUrl.yml
    variables:
        redirectUrl: ""
        accountId: ${ENV(accountId_sunyang)}
        platform: 1
        subjectId: ${ENV(accountId_sunyang)}
        processId: $processId1
        code: 0
        message: 成功
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]
    extract:
    - processId2: content.data.processId
    - resultUrl2: content.data.resultUrl


- test:
    name:  获取密文(个人用户)
    variables:
        userId: sytest001
    api: api/footstone_user_api/encryptByPublicKey.yml
    extract:
    - encryptContent1: content.data

- test:
    name:  换取免登短链
    variables:
      encryptContent: $encryptContent1
      resultUrl1: $resultUrl2
    api: api/footstone_user_api/eloginSign.yml


- test:
      name: 直接发起,获取B企业的流程操作地址
      api: api/open_flowtemplate/getProcessUrl.yml
      variables:
          redirectUrl: ""
          accountId: ${ENV(accountId_sunyang)}
          platform: 1
          subjectId: ${ENV(org_accountId)}
          processId: $processId1
          code: 0
          message: 成功
      validate:
      - eq: [content.code, $code]
      - eq: [content.message, $message]
      extract:
      - processId3: content.data.processId
      - resultUrl3: content.data.resultUrl


- test:
    name:  获取密文(企业用户)
    variables:
      userId: sytest001
    api: api/footstone_user_api/encryptByPublicKey.yml
    extract:
    - encryptContent2: content.data

- test:
    name:  换取免登短链
    variables:
      resultUrl1: $resultUrl3
      encryptContent: $encryptContent2
    api: api/footstone_user_api/eloginSign.yml
