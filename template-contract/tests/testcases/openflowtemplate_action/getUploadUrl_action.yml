- config:
    name: 文件直传创建文件
#    base_url: ${ENV(contract_manager_url)}


- test:
    name: 文件直传创建文件
    api: api/open_flowtemplate/getUploadUrl.yml
    variables:
      contentMd5: m49PzxWxAG8sMwNkZrlJDw==
      convert2Pdf: false
      fileName: ${generate_random_str(10)}
      fileSize: 500
      operatorId: ${ENV(accountId)}
      tenantId: ${ENV(accountId)}
      code: 0
      message: 成功

    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]
    extract:
      - fileId: content.data.fileId
      - uploadUrl: content.data.uploadUrl



#- test:
#    name: contentMd5为空
#    api: api/open_flowtemplate/getUploadUrl.yml
#    variables:
#      contentMd5: null
#      code: ********
#      message:  "参数错误: 文件md5不能为空"
#    validate:
#    - eq: [content.code, $code]
#    - eq: [content.message, $message]


- test:
    name: fileSize为0
    api: api/open_flowtemplate/getUploadUrl.yml
    variables:
      fileSize: 0
      code: ********
      message:  "参数错误: 文件大小不能为0"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: fileName为空
    api: api/open_flowtemplate/getUploadUrl.yml
    variables:
      fileName: null
      code: ********
      message:  "参数错误: 文件名字不能为空"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: operatorId为空
    api: api/open_flowtemplate/getUploadUrl.yml
    variables:
      operatorId: null
      code: ********
      message:  "参数错误: operatorId不能为空"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: tenantId为空
    api: api/open_flowtemplate/getUploadUrl.yml
    variables:
      tenantId: null
      code: ********
      message:  "参数错误: tenantId不能为空"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]



- test:
    name: 上传文件到oss
    variables:
      uploadUrl: $uploadUrl
      contentMd5: $contentMd5
      contentType: $contentType
      filePath: $filePath
      binary: ${open_file($filePath)}
    api: api/files/upload_oss.yml
    validate:
    - eq: ["content.errCode", 0]
    - eq: ["content.msg", "成功"]
