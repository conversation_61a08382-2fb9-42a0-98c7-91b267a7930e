- config:
    name: 轩辕api模板发起
    base_url: ${ENV(xy_open_url)}
    output:
      - processId1
    variables:
      solutionNum: ${ENV(solutionNum_identy_medium)}
      flowTemplateId: ${ENV(template_flowTemplateId01)}
      participantId: ${ENV(participantId_sunyang)}
      initiatorAccountId: ${ENV(accountId)}
      accoutOid: ${ENV(accountId_sunyang)}
      subjectId: ${ENV(accountId_sunyang)}
      operatorId: ${ENV(accountId)}
      paltform: 1
      tenantId: ${ENV(org_accountId)}
      tenantId1: ${ENV(org_accountId)}
      taskName: ${generate_random_str(10)}
      accountName: 孙阳
      account: ***********
      subjectName: 孙阳
      subjectType: 0
      accountName2: 张圆圆
      account2: ***********
      subjectName2: 张圆圆
      subjectType2: 0
      accoutOid2: ${ENV(accountId)}
      subjectId2: ${ENV(accountId)}
      accountInfo: $accountInfo
      code: 0
      message: 成功
      code1: 0
      message1: 成功



- test:
    name: 获取流程模板信息
    api: api/open_flowtemplate/getFlowtemplateInfo.yml
    variables:
      flowTemplateId: $flowTemplateId
      operatorId: $operatorId
      tenantId: $tenantId1

    validate:
    - eq: ["content.code", $code1]
    - eq: ["content.message", $message1]
    extract:
    - participantId_sunyang: content.data.participants.1.participantId
    - participantId_zyy: content.data.participants.0.participantId


- test:
    name: 校验发起的接口
    api: api/open_flowtemplate/startFlowtemplate1.yml
    variables:
        json:
          {
            "addAttachements": null,
            "addCcs": null,
            "fileEndTime": *************,
            "initiatorAccountId":$initiatorAccountId,
            "paltform": $paltform,
            "participants": [
            {
              "instances": [
                $accountInfo
              ],
              "participantId": $participantId_zyy
            },
            {
              "instances": [
                $accountInfo1
              ],
              "participantId": $participantId_sunyang
            }
            ],
            "redirectUrl": "",
            "signEndTime": *************,
            "skipFill": false,
            "taskName": $taskName,
            "tenantId": $tenantId,
            "solutionNum":$solutionNum
          }


    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]
    extract:
    - processId1: content.data.processId
#    - resultUrl1: content.data.resultUrl

- test:
    name: 直接发起,获取A个人的流程操作地址
    api: api/open_flowtemplate/getProcessUrl.yml
    variables:
      redirectUrl: ""
      accountId: ${ENV(accountId_sunyang)}
      platform: 1
      subjectId: ${ENV(accountId_sunyang)}
      processId: $processId1
      code: 0
      message: 成功
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]
    extract:
    - processId2: content.data.processId
    - resultUrl2: content.data.resultUrl


- test:
    name:  获取密文(个人用户)
    variables:
      userId: sytest001
    api: api/footstone_user_api/encryptByPublicKey.yml
    extract:
    - encryptContent1: content.data

- test:
    name:  换取免登短链
    variables:
      encryptContent: $encryptContent1
      resultUrl1: $resultUrl2
    api: api/footstone_user_api/eloginSign.yml

