- config:
    name: 轩辕api个人发起
    base_url: ${ENV(xy_open_url)}

- test:
    name: 校验发起的接口
    api: api/open_flowtemplate/startFlowtemplate.yml
    variables:
      initiatorAccountId: ${ENV(accountId)}
      flowTemplateId: ${ENV(flowtemlateId_initiate)}
      participantId: ${ENV(participantId_sunyang)}
      accoutOid: ${ENV(accountId_sunyang)}
      code: 0
      message: 成功
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: initiatorAccountId发起人账号为空
    api: api/open_flowtemplate/startFlowtemplate.yml
    variables:
      initiatorAccountId: null
      code: ********
      message:  "参数错误: initiatorAccountId不能为空"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]



- test:
    name: initiatorAccountId发起人账号不存在
    api: api/open_flowtemplate/startFlowtemplate.yml
    variables:
      initiatorAccountId: "4327587436543598435"
      code: *********
      message: 账号不存在或已注销
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: paltform为2，H5
    api: api/open_flowtemplate/startFlowtemplate.yml
    variables:
      paltform: 2
      code: 0
      message: 成功
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: paltform不传，理应默认为1
    api: api/open_flowtemplate/startFlowtemplate.yml
    variables:
      paltform: null
      code: 0
      message: 成功
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: paltform为1
    api: api/open_flowtemplate/startFlowtemplate.yml
    variables:
      paltform: 1
      code: 0
      message: 成功
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: paltform为3，case失败
    api: api/open_flowtemplate/startFlowtemplate.yml
    variables:
      paltform: 3
      code: ********
      message: "参数错误: paltform值不正确"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]




- test:
    name: participantId 为空
    api: api/open_flowtemplate/startFlowtemplate.yml
    variables:
      participantId: null
      code: ********
      message: "参数错误: participantId不能为空"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: participantId 不存在
    api: api/open_flowtemplate/startFlowtemplate.yml
    variables:
      participantId: 321432143214
      code: ********
      message: "参数错误: 参与方不存在或不全"

    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: taskName 字段长度大于100
    api: api/open_flowtemplate/startFlowtemplate.yml
    variables:
      taskName: ${generate_random_str(200)}
      code: ********
      message: "参数错误: taskName最多50个字符" #原错误码：cooperationName长度最高为100
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: accountName为空
    api: api/open_flowtemplate/startFlowtemplate.yml
    variables:
      accountName: null
      code: ********
      message: "参数错误: accountName不能为空"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: account为空
    api: api/open_flowtemplate/startFlowtemplate.yml
    variables:
      account: null
      code: ********
      message: "参数错误: account不能为空"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: subjectName为空
    api: api/open_flowtemplate/startFlowtemplate.yml
    variables:
      subjectName: null
      code: ********
      message: "参数错误: subjectName不能为空"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: subjectType为空
    api: api/open_flowtemplate/startFlowtemplate.yml
    variables:
      subjectType: null
      code: ********
      message: "参数错误: subjectType不能为空"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: solutionNum为空
    api: api/open_flowtemplate/startFlowtemplate.yml
    variables:
      solutionNum: null
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: solutionNum长度超100
    api: api/open_flowtemplate/startFlowtemplate.yml
    variables:
      solutionNum: CJ0FsiZo5u
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]




