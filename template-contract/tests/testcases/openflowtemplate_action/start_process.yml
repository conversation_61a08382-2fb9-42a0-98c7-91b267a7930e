- config:
    name: 轩辕api个人直接发起
#    base_url: ${ENV(contract_manager_url)}



- test:
    name: 文件直传创建文件
    variables:
    - json: {}
    api: api/open_flowtemplate/getUploadUrl.yml
    extract:
    - fileId: content.data.fileId
    - uploadUrl: content.data.uploadUrl
#    teardown_hooks:
#      - ${hook_sleep_n_secs(5)}


- test:
    name: 上传文件到oss
    variables:
      uploadUrl: $uploadUrl
      contentMd5: m49PzxWxAG8sMwNkZrlJDw==


    api: api/files/upload_oss.yml
    validate:
    - eq: ["content.errCode", 0]
    - eq: ["content.msg", "成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}


- test:
    name: 校验发起的接口
    api: api/open_flowtemplate/startProcess.yml
    variables:
      initiatorAccountId: ${ENV(accountId)}
      participantLabel: 甲方
      accoutOid: ${ENV(accountId_sunyang)}
#      fileId: ${ENV(fileId)}
      code: 0
      message: 成功
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: initiatorAccountId发起人账号为空
    api: api/open_flowtemplate/startProcess.yml
    variables:
      initiatorAccountId: null
      code: ********
      message:  "参数错误: initiatorAccountId不能为空"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]



- test:
    name: initiatorAccountId发起人账号不存在
    api: api/open_flowtemplate/startProcess.yml
    variables:
      initiatorAccountId: "4327587436543598435"
      code: *********
      message: 账号不存在或已注销
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: paltform为2，H5
    api: api/open_flowtemplate/startProcess.yml
    variables:
      paltform: 2
      code: 0
      message: 成功
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: paltform不传，理应默认为1
    api: api/open_flowtemplate/startProcess.yml
    variables:
      paltform: null
      code: 0
      message: 成功
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: paltform为1
    api: api/open_flowtemplate/startProcess.yml
    variables:
      paltform: 1
      code: 0
      message: 成功
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: paltform为3，case失败
    api: api/open_flowtemplate/startProcess.yml
    variables:
      paltform: 3
      code: ********
      message: "参数错误: paltform值不正确"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]




- test:
    name: participantLabel 为空
    api: api/open_flowtemplate/startProcess.yml
    variables:
      participantLabel: null
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: participantLabel 随机数
    api: api/open_flowtemplate/startProcess.yml
    variables:
      participantLabel: 321432143214
      code: 0
      message: "成功"

    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: taskName 字段长度大于100
    api: api/open_flowtemplate/startProcess.yml
    variables:
      taskName: ${generate_random_str(200)}
      code: ********
      message: "参数错误: taskName最多50个字符" #原错误码：cooperationName长度最高为100
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: accountName为空
    api: api/open_flowtemplate/startProcess.yml
    variables:
      accountName: null
      code: ********
      message: "参数错误: accountName不能为空"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: account为空
    api: api/open_flowtemplate/startProcess.yml
    variables:
      account: null
      code: ********
      message: "参数错误: account不能为空"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: subjectName为空
    api: api/open_flowtemplate/startProcess.yml
    variables:
      subjectName: null
      code: ********
      message: "参数错误: subjectName不能为空"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: subjectType为空
    api: api/open_flowtemplate/startProcess.yml
    variables:
      subjectType: null
      code: ********
      message: "参数错误: subjectType不能为空"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: solutionNum为空
    api: api/open_flowtemplate/startProcess.yml
    variables:
      solutionNum: null
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: needPositionSign 是true
    api: api/open_flowtemplate/startProcess.yml
    variables:
      needPositionSign: true
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: signedNoticeUrl 签署完成回调地址设定
    api: api/open_flowtemplate/startProcess.yml
    variables:
      signedNoticeUrl: "http://118.178.93.198:8082/testnotify/msgRecive"
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: sealType2 企业印章类型不传(非指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType2: []
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]
    extract:
    - processId1: content.data.processId
    - resultUrl1: content.data.resultUrl


- test:
    name: sealType2 企业印章类型3,企业印章(非指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType2: [3]
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]
    extract:
    - processId2: content.data.processId
    - resultUrl2: content.data.resultUrl


- test:
    name: sealType2 企业印章类型4,法人代表印章(非指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType2: [4]
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]
    extract:
    - processId3: content.data.processId
    - resultUrl3: content.data.resultUrl


- test:
    name: sealType2 企业印章类型3,4,企业章+法人代表印章(非指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType2: [4,3]
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]
    extract:
    - processId4: content.data.processId
    - resultUrl4: content.data.resultUrl


- test:
    name: sealType 个人印章不传(非指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType: []
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]
    extract:
    - processId5: content.data.processId
    - resultUrl5: content.data.resultUrl


- test:
    name: sealType 个人印章，手绘印章(非指定位置)-异常校验
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType: [0]
      code: ********
      message: "参数错误: 印章类型不正确"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: sealType 个人印章，手绘印章(非指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType: [1]
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]
    extract:
    - processId6: content.data.processId
    - resultUrl6: content.data.resultUrl

- test:
    name: sealType 个人印章，模板印章(非指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType: [2]
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]
    extract:
    - processId7: content.data.processId
    - resultUrl7: content.data.resultUrl

- test:
    name: sealType 个人印章，手绘印章，模板印章(非指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType: [1,2]
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]
    extract:
    - processId8: content.data.processId
    - resultUrl8: content.data.resultUrl


- test:
    name: sealType2 企业印章类型不传(指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType2: []
      needPositionSign: true
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]
    extract:
    - resultUrl9: content.data.resultUrl


- test:
    name: sealType2 企业印章类型2,企业印章(指定位置)-异常校验
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType2: [2]
      needPositionSign: true
      code: ********
      message: "参数错误: 参与方印章类型与主体类型不匹配"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: sealType2 企业印章类型3,企业印章(指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType2: [3]
      needPositionSign: true
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: sealType2 企业印章类型4,法人代表印章(指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType2: [4]
      needPositionSign: true
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: sealType2 企业印章类型3,4,企业章+法人代表印章(指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType2: [4,3]
      needPositionSign: true
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: sealType 个人印章不传(指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType: []
      needPositionSign: true
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]


- test:
    name: sealType 个人印章，手绘印章(指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType: [1]
      needPositionSign: true
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: sealType 个人印章，模板印章(指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType: [2]
      needPositionSign: true
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]

- test:
    name: sealType 个人印章，手绘印章，模板印章(指定位置)
    api: api/open_flowtemplate/startProcess.yml
    variables:
      sealType: [1,2]
      needPositionSign: true
      code: 0
      message: "成功"
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]




