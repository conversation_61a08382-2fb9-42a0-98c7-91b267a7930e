- config:
    name: 轩辕api个人直接批量发起
#    base_url: ${ENV(contract_manager_url)}



- test:
    name: 文件直传创建文件
    variables:
    - json: {}
    api: api/open_flowtemplate/getUploadUrl.yml
    extract:
    - fileId: content.data.fileId
    - uploadUrl: content.data.uploadUrl
#    teardown_hooks:
#      - ${hook_sleep_n_secs(5)}


- test:
    name: 上传文件到oss
    variables:
      uploadUrl: $uploadUrl
      contentMd5: m49PzxWxAG8sMwNkZrlJDw==


    api: api/files/upload_oss.yml
    validate:
    - eq: ["content.errCode", 0]
    - eq: ["content.msg", "成功"]
    teardown_hooks:
      - ${hook_sleep_n_secs(5)}


- test:
    name: 校验发起的接口
    api: api/open_flowtemplate/startProcess1.yml
    variables:
#      json:
      json:
        {
          "attachements":  [
          {
            "fileId": $cc_fileId,
            "fileName":$fileName
          }
          ],
          "ccs": [
          {
            "account": $cc_account,
            "accountName": $cc_accountName,
            "accountOid": "",
            "subjectId": $cc_subjectId,
            "subjectName": $cc_subjectName,
            "subjectType": 0
          }
          ],
          "fileEndTime": *************,
          "initiatorAccountId": $initiatorAccountId,
          "needPositionSign":$needPositionSign,
          "paltform": $paltform,
          "participants": [
          {
            "instances": [
            {
              "accountInfo":{
                "account": $account,
                "accountName": $accountName,
                "accountOid": $accoutOid,
                "subjectId": $accoutOid,
                "subjectName": $subjectName,
                "subjectType": $subjectType
              },
              "subTaskName": ""
            }
            ],
            "participantLabel":$participantLabel,
            "sealTypes": $sealType,
            "signOrder": 0
          },

          {
            "instances": [
            {
              "accountInfo":{
                "account": $account2,
                "accountName": $accountName2,
                "accountOid": $accoutOid2,
                "subjectId": $subjectId2,
                "subjectName": $subjectName2,
                "subjectType": $subjectType2
              },
              "subTaskName": ""
            },
            {
              "accountInfo":{
                "account": $account2,
                "accountName": $accountName2,
                "accountOid": $accoutOid2,
                "subjectId": $subjectId2,
                "subjectName": $subjectName2,
                "subjectType": $subjectType2
              },
              "subTaskName": ""
            },
            {
              "accountInfo":{
                "account": $account2,
                "accountName": $accountName2,
                "accountOid": $accoutOid2,
                "subjectId": $subjectId2,
                "subjectName": $subjectName2,
                "subjectType": $subjectType2
              },
              "subTaskName": ""
            },
            {
              "accountInfo":{
                "account": $account2,
                "accountName": $accountName2,
                "accountOid": $accoutOid2,
                "subjectId": $subjectId2,
                "subjectName": $subjectName2,
                "subjectType": $subjectType2
              },
              "subTaskName": ""
            },
            {
              "accountInfo":{
                "account": $account2,
                "accountName": $accountName2,
                "accountOid": $accoutOid2,
                "subjectId": $subjectId2,
                "subjectName": $subjectName2,
                "subjectType": $subjectType2
              },
              "subTaskName": ""
            },
            {
              "accountInfo":{
                "account": $account2,
                "accountName": $accountName2,
                "accountOid": $accoutOid2,
                "subjectId": $subjectId2,
                "subjectName": $subjectName2,
                "subjectType": $subjectType2
              },
              "subTaskName": ""
            },
            {
              "accountInfo":{
                "account": $account2,
                "accountName": $accountName2,
                "accountOid": $accoutOid2,
                "subjectId": $subjectId2,
                "subjectName": $subjectName2,
                "subjectType": $subjectType2
              },
              "subTaskName": ""
            },
            {
              "accountInfo":{
                "account": $account2,
                "accountName": $accountName2,
                "accountOid": $accoutOid2,
                "subjectId": $subjectId2,
                "subjectName": $subjectName2,
                "subjectType": $subjectType2
              },
              "subTaskName": ""
            },
            {
              "accountInfo":{
                "account": $account2,
                "accountName": $accountName2,
                "accountOid": $accoutOid2,
                "subjectId": $subjectId2,
                "subjectName": $subjectName2,
                "subjectType": $subjectType2
              },
              "subTaskName": ""
            },
            {
              "accountInfo":{
                "account": $account2,
                "accountName": $accountName2,
                "accountOid": $accoutOid2,
                "subjectId": $subjectId2,
                "subjectName": $subjectName2,
                "subjectType": $subjectType2
              },
              "subTaskName": ""
            },
            {
              "accountInfo":{
                "account": $account2,
                "accountName": $accountName2,
                "accountOid": $accoutOid2,
                "subjectId": $subjectId2,
                "subjectName": $subjectName2,
                "subjectType": $subjectType2
              },
              "subTaskName": ""
            }
            ],
            "participantLabel":$participantLabel2,
            "sealTypes": $sealType2,
            "signOrder": 0
          }
          ],
          "redirectUrl": "",
          "signEndTime": *************,
          "signFiles": [
          {
            "fileId": $fileId,
            "fileName": $fileName
          }
          ],
          "skipFill": false,
          "taskName": $taskName,
          "tenantId": $tenantId,
          "signedNoticeUrl":$signedNoticeUrl,
#          "solutionNum":$solutionNum
        }

      code: 0
      message: 成功
    validate:
    - eq: [content.code, $code]
    - eq: [content.message, $message]




