- config:
    name: 获取签署任务概要

- test:
    name: 获取签署任务概要-Y
    api: api/sasstianyin_process/flows_outline.yml
    variables:
      flowId: ${ENV(saaatianyin_flowId)}
      accountId: ${ENV(sasstiany_accountId)}
      code: 0
      message: "成功"

- test:
    name: 获取签署任务概要-flowId不匹配
    api: api/sasstianyin_process/flows_outline.yml
    variables:
      flowId: "123456789987654321"
      accountId: ${ENV(sasstiany_accountId)}
      code: ********
      message: "流程不存在"

- test:
    name: 获取签署任务概要-accountId不匹配
    api: api/sasstianyin_process/flows_outline.yml
    variables:
      flowId: ${ENV(saaatianyin_flowId)}
      accountId: "123456789987654321"
      code: 0
      message: "成功"

- test:
    name: 获取签署任务概要-flowId为空
    api: api/sasstianyin_process/flows_outline.yml
    variables:
      flowId: ""
      accountId: ${ENV(sasstiany_accountId)}
      code: ********
      message: "参数错误: 签署流程id不能为空"

- test:
    name: 获取签署任务概要-accountId为空
    api: api/sasstianyin_process/flows_outline.yml
    variables:
      flowId: ${ENV(saaatianyin_flowId)}
      accountId: ""
      code: ********
      message: "参数错误: 个人Oid不为空"