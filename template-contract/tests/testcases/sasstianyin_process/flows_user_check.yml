- config:
    name: 判断用户是否有天印流程

- test:
    name: 判断用户是否有天印流程-Y
    api: api/sasstianyin_process/flows_user_check.yml
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      type: 0
      subjectId: ${ENV(sasstiany_accountId)}
      code: 0
      message: "成功"

- test:
    name: 判断用户是否有天印流程-用户可查看的
    api: api/sasstianyin_process/flows_user_check.yml
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      type: 1
      subjectId: ${ENV(sasstiany_accountId)}
      code: 0
      message: "成功"

- test:
    name: 判断用户是否有天印流程-可出证的
    api: api/sasstianyin_process/flows_user_check.yml
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      type: 2
      subjectId: ${ENV(sasstiany_accountId)}
      code: 0
      message: "成功"

- test:
    name: 判断用户是否有天印流程-N
    api: api/sasstianyin_process/flows_user_check.yml
    variables:
      accountId: ${ENV(sasstianyin_subjectId_oid)}
      type: 1
      subjectId: ${ENV(sasstianyin_subjectId_oid)}
      code: 0
      message: "成功"

- test:
    name: 判断用户是否有天印流程-accountId不匹配
    api: api/sasstianyin_process/flows_user_check.yml
    variables:
      accountId: "123456789987654321"
      type: 1
      subjectId: ${ENV(sasstiany_accountId)}
      code: *********
      message: "账号不存在或已注销"

- test:
    name: 判断用户是否有天印流程-subjectId不匹配
    api: api/sasstianyin_process/flows_user_check.yml
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      type: 0
      subjectId: ${ENV(sasstianyin_subjectId_oid)}
      code: 0
      message: "成功"

- test:
    name: 判断用户是否有天印流程-type不匹配
    api: api/sasstianyin_process/flows_user_check.yml
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      type: 3
      subjectId: ${ENV(sasstiany_accountId)}
      code: 0
      message: "成功"

- test:
    name: 判断用户是否有天印流程-企业流程
    api: api/sasstianyin_process/flows_user_check.yml
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      type: 0
      subjectId: ${ENV(sasstianyin_subjectId_org)}
      code: 0
      message: "成功"