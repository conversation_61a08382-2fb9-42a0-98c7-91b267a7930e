- config:
    name: 获取用户可查看的企业天印流程列表

- test:
    name: 获取用户可查看的企业天印流程列表-Y
    api: api/sasstianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      flowName: ""
      subjectId: ${ENV(sasstiany_accountId)}
      status: ""
      code: 0
      message: "成功"

- test:
    name: 获取用户可查看的企业天印流程列表-N
    api: api/sasstianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      flowName: ""
      subjectId: ${ENV(sasstianyin_subjectId_oid)}
      status: ""
      code: 0
      message: "成功"

- test:
    name: 获取用户可查看的企业天印流程列表-flowName模糊查询
    api: api/sasstianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      flowName: "个人"
      subjectId: ${ENV(sasstiany_accountId)}
      status: ""
      code: 0
      message: "成功"

- test:
    name: 获取用户可查看的企业天印流程列表-流程状态签署中
    api: api/sasstianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      flowName: ""
      subjectId: ${ENV(sasstiany_accountId)}
      status: 1
      code: 0
      message: "成功"

- test:
    name: 获取用户可查看的企业天印流程列表-流程状态完成
    api: api/sasstianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      flowName: ""
      subjectId: ${ENV(sasstiany_accountId)}
      status: 2
      code: 0
      message: "成功"

- test:
    name: 获取用户可查看的企业天印流程列表-流程状态撤回
    api: api/sasstianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      flowName: ""
      subjectId: ${ENV(sasstiany_accountId)}
      status: 3
      code: 0
      message: "成功"

- test:
    name: 获取用户可查看的企业天印流程列表-流程状态拒签
    api: api/sasstianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      flowName: ""
      subjectId: ${ENV(sasstiany_accountId)}
      status: 4
      code: 0
      message: "成功"

- test:
    name: 获取用户可查看的企业天印流程列表-流程状态过期
    api: api/sasstianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      flowName: ""
      subjectId: ${ENV(sasstiany_accountId)}
      status: 5
      code: 0
      message: "成功"

- test:
    name: 获取用户可查看的企业天印流程列表-流程状态作废
    api: api/sasstianyin_process/flows_view_list.yml
    variables:
      accountId: ${ENV(sasstiany_accountId)}
      flowName: ""
      subjectId: ${ENV(sasstiany_accountId)}
      status: 6
      code: 0
      message: "成功"