- config:
    name: 新增一条规则并生效
    base_url: ${ENV(contract_manager_url)}
    variables:
        - code: 0
        - message: 成功
        - name1: effrule-${getTimeStamp()}


- test:
      name: 上传台账规则模板获取规则id
      variables:
        accountId: ${ENV(ouid)}
        tenantId: ${ENV(orgid2)}
        fileId: ""
        ruleTemplateId: null
        flowTemplateId: ${ENV(flowTemplateId1)}
      api: api/standing_book2.0_api/create_templaterule_id.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - ruleTemplateId: content.data

- test:
    name: 创建归档目录-获取menuId
    api: api/groupingdir/creategrouping_menus.yml
    variables:
      accountId1: ${ENV(ouid)}
      createTenantId: ${ENV(orgid2)}
      parentMenuId:
      name: effect-${getTimeStamp()}
    teardown_hooks:
        - ${teardown_hook_sleep_N_secs(1)}
    extract:
        - menuId: content.data
    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]

- test:
      name: 新增台账规则
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: ""
          - menuList: $menuId
          - name: $name1
          - remark: "青莲在测试"
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                "ruleTemplateMenuId": $menuId
                "ruleTemplateId": $ruleTemplateId

      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", $code]
          - contains: ["content.message", $message]
      extract:
        - ruleId: content.data.ruleId

- test:
      name: 查询规则详情-获取字段Id
      variables:
          - ruleId: $ruleId
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
      api: api/standing_book2.0_api/list_rule_detail.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - fieldId: content.data.ruleFieldList.0.fieldId

- test:
      name: 查询台账规则模板详情-获取控件Id
      variables:
          - ruleId: $ruleId
          - ruleTemplateId: $ruleTemplateId
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
      api: api/standing_book2.0_api/list_template_detail.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - structId: content.data.structList.0.structId

- test:
      name: 关联模板字段
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - ruleTemplateList:
                ruleConfigList:
                    - structId: $structId
                      fieldId: $fieldId
                      structType: 1
                ruleTemplateId: $ruleTemplateId

      api: api/standing_book2.0_api/relevance_rule.yml
      validate:
          - eq: ["content.code", $code]
          - contains: ["content.message", $message]

- test:
      name: 查询规则详情-规则是生效状态
      variables:
          - ruleId: $ruleId
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
      api: api/standing_book2.0_api/list_rule_detail.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

- test:
      name: 删除新增的台账规则
      variables:
          - ruleId: $ruleId
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
      api: api/standing_book2.0_api/delete_rule.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

- test:
    name: 删除归档目录
    api: api/groupingdir/deletegrouping_menus.yml
    variables:
      menuId: $menuId
      accountId2: ${ENV(ouid)}
      deleteTenantId: ${ENV(orgid2)}
    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]