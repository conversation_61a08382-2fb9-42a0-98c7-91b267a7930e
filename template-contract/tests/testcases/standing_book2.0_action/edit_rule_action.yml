- config:
    name: 编辑一条台账规则
    base_url: ${ENV(contract_manager_url)}
    variables:
        - code: 0
        - message: 成功
        - name1: editrule-${getTimeStamp()}


- test:
      name: 上传台账规则模板-获取规则id
      variables:
        accountId: ${ENV(ouid)}
        tenantId: ${ENV(orgid2)}
        fileId: ""
        ruleTemplateId: null
        flowTemplateId: ${ENV(flowTemplateId2)}
      api: api/standing_book2.0_api/create_templaterule_id.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - ruleTemplateId: content.data

- test:
    name: 创建归档目录-获取menuId
    api: api/groupingdir/creategrouping_menus.yml
    variables:
      accountId1: ${ENV(ouid)}
      createTenantId: ${ENV(orgid2)}
      parentMenuId:
      name: "edit-${getTimeStamp()}"
    teardown_hooks:
        - ${teardown_hook_sleep_N_secs(1)}
    extract:
        - menuId: content.data
    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]

- test:
    name: 创建归档目录-获取menuId2
    api: api/groupingdir/creategrouping_menus.yml
    variables:
      accountId1: ${ENV(ouid)}
      createTenantId: ${ENV(orgid2)}
      parentMenuId: $menuId
      name: "edit2-${getTimeStamp()}"
    teardown_hooks:
        - ${teardown_hook_sleep_N_secs(1)}
    extract:
        - menuId2: content.data
    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]

- test:
    name: 创建归档目录-获取menuId3
    api: api/groupingdir/creategrouping_menus.yml
    variables:
      accountId1: ${ENV(ouid)}
      createTenantId: ${ENV(orgid2)}
      parentMenuId: ""
      name: "edit3-${getTimeStamp()}"
    extract:
        - menuId3: content.data
    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]

- test:
      name: 新增台账规则
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: ""
          - menuList: $menuId
          - name: $name1
          - remark: "青莲在测试"
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                "ruleTemplateMenuId": $menuId
                "ruleTemplateId": $ruleTemplateId

      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", $code]
          - contains: ["content.message", $message]
      extract:
          - ruleId: content.data.ruleId

- test:
      name: 编辑台账规则-编辑oid为空
      variables:
          - accountId: ""
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - menuList: $menuId
          - name: $name1
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                "ruleTemplateMenuId": $menuId
                "ruleTemplateId": $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "无操作权限"]

- test:
      name: 编辑台账规则-编辑租户id为空
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ""
          - ruleId: $ruleId
          - menuList: $menuId
          - name: $name1
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                "ruleTemplateMenuId": $menuId
                "ruleTemplateId": $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "缺少参数"]

- test:
      name: 编辑台账规则-编辑租户id与oid不匹配
      variables:
          - accountId: ${ENV(accountId_sunyang)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - menuList: $menuId
          - name: $name1
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                "ruleTemplateMenuId": $menuId
                "ruleTemplateId": $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "无操作权限"]

- test:
      name: 新增台账规则-name长度超过20字符
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - menuList: $menuId
          - name: "name长度超过20字符name长度超过2"
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: $menuId,
                ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "台账规则名称最多20个字符"]

- test:
      name: 新增台账规则-remark长度超过50字符
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - menuList: $menuId
          - name: $name1
          - remark: "新增一个台账规则20新增一个台账规则20新增一个台账规则20新增一个台账规则20新增一个台账规则20x"
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: $menuId,
                ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - eq: ["content.message", "参数错误: 台账规则描述最多50字符"]

- test:
      name: 编辑台账规则-menuList为空
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - menuList: ""
          - name: $name1
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: $menuId
                ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "分类不存在"]

- test:
      name: 编辑台账规则-分类id非一级分类id
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - menuList: $menuId2
          - name: $name1
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: $menuId
                ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "台账规则只能绑定一级分类"]

#- test:
#      name: 新增台账规则-当前分类被其他台账规则关联
#      variables:
#          - accountId: ${ENV(ouid)}
#          - tenantId: ${ENV(orgid2)}
#          - ruleId: ""
#          - menuList: ""
#          - name: $name
#          - remark: ""
#          - ruleFieldList:
#              - fieldId: ""
#                fieldName: "姓名"
#                fieldType: ""
#          - ruleTemplateMenuList:
#              - ruleTemplateMenuId: "33",
#                ruleTemplateId: $ruleTemplateId
#      api: api/standing_book2.0_api/save_rule.yml
#      validate:
#          - eq: ["content.code", $code]
#          - eq: ["content.message", $message]
#
- test:
      name: 当前分类被删除或被移动成二级或三级分类
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - menuList: ${ENV(menuId_delete)}
          - name: $name1
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: ""
          - ruleTemplateMenuList:
                ruleTemplateMenuId: $menuId
                ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "分类不存在"]

- test:
      name: 新增台账规则-表头扩展字段为空
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - menuList: $menuId
          - name: $name1
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: ""
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: $menuId,
                ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "参数错误: 扩展字段名称不能为空"]

- test:
      name: 新增台账规则-表头扩展字段名称大于20个字符
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - menuList: $menuId
          - name: $name1
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "大于20字符大于20字符大于20字符222"
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: $menuId,
                ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "参数错误"]


- test:
      name: 编辑台账规则-模板所属分类为空
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - menuList: $menuId
          - name: $name1
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: ""
                ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "分类不存在"]

- test:
      name: 编辑台账规则-模板所属分类不存在
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - menuList: $menuId
          - name: $name1
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: "2523xxxwqrwqtweye214"
                ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "分类不存在"]

#- test:
#      name: 编辑台账规则-非当前企业所选分类或其子分类
#      variables:
#          - accountId: ${ENV(ouid)}
#          - tenantId: ${ENV(orgid2)}
#          - ruleId: $ruleId
#          - menuList: $menuId
#          - name: $name
#          - remark: ""
#          - ruleFieldList:
#                fieldId: ""
#                fieldName: "姓名"
#                fieldType: 1
#          - ruleTemplateMenuList:
#                ruleTemplateMenuId: $menuId3
#                ruleTemplateId: $ruleTemplateId
#      api: api/standing_book2.0_api/save_rule.yml
#      validate:
#          - eq: ["content.code", ********]
#          - eq: ["content.message", "分类不存在"]

- test:
      name: 编辑台账规则-模板id为空
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - menuList: $menuId
          - name: $name1
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: $menuId2
                ruleTemplateId: ""
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - eq: ["content.message", "无操作权限"]

- test:
      name: 编辑台账规则-模板id不存在
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - menuList: $menuId
          - name: $name1
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: $menuId2
                ruleTemplateId: "xxxxugdweidugi214525"
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - eq: ["content.message", "无操作权限"]

- test:
      name: 查询规则详情-获取字段Id
      variables:
          - ruleId: $ruleId
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
      api: api/standing_book2.0_api/list_rule_detail.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - fieldId: content.data.ruleFieldList.0.fieldId

- test:
      name: 查询台账规则模板详情-获取控件Id
      variables:
          - ruleId: $ruleId
          - ruleTemplateId: $ruleTemplateId
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
      api: api/standing_book2.0_api/list_template_detail.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - structId: content.data.structList.0.structId

- test:
      name: 关联模板字段
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - ruleTemplateList:
                ruleConfigList:
                    - structId: $structId
                      fieldId: $fieldId
                      structType: 1
                ruleTemplateId: $ruleTemplateId

      api: api/standing_book2.0_api/relevance_rule.yml
      validate:
          - eq: ["content.code", 0]
          - contains: ["content.message", "成功"]

- test:
      name: 查询规则详情-规则是生效状态
      variables:
          - ruleId: $ruleId
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
      api: api/standing_book2.0_api/list_rule_detail.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

- test:
      name: 删除新增的台账规则
      variables:
          - ruleId: $ruleId
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
      api: api/standing_book2.0_api/delete_rule.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

- test:
    name: 删除归档目录-menuId
    api: api/groupingdir/deletegrouping_menus.yml
    variables:
      menuId: $menuId
      accountId2: ${ENV(ouid)}
      deleteTenantId: ${ENV(orgid2)}
    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]

- test:
    name: 删除归档目录-menuId3
    api: api/groupingdir/deletegrouping_menus_two.yml
    variables:
      movemenuId: $menuId3
      deleteoid2: ${ENV(ouid)}
      deleteTenantId2: ${ENV(orgid2)}
    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]