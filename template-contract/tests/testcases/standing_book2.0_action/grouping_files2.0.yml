- config:
    name: 智能台账2.0-流程归档
    base_url: ${ENV(contract_manager_url)}
    variables:
        - code: 0
        - message: 成功

- test:
      name: 查询分类列表-获取可绑定分类id
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          ruleId: ""
      api: api/standing_book2.0_api/list_menu.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - menuId: content.data.0.menuId
          - menuId2: content.data.0.childNode.0.menuId
          - menuId3: content.data.1.menuId

- test:
      name: 待归档列表查询
      variables:
          unquerylistoid: ${ENV(ouid)}
          unquerylistTenantId: ${ENV(orgid2)}
          menuId: ""
          title: ""
          initiator: ""
          createFrom: ""
          createEnd: ""
      api: api/groupingquery/ungroupinglistquery.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - processId: content.data.groupingProcessList.0.processId
          - processId2: content.data.groupingProcessList.1.processId

- test:
      name: 流程归档-accountId为空
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuIdList: ""
          processIdList:
            - $processId
      api: api/standing_book2.0_api/grouping_files2.0.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "归档分类不能为空"]

- test:
      name: 流程归档-accountId不存在
      variables:
          accountId: "xxxxnoexitoid"
          tenantId: ${ENV(orgid2)}
          menuIdList: ""
          processIdList:
             - $processId
      api: api/standing_book2.0_api/grouping_files2.0.yml
      validate:
          - eq: ["content.code", *********]
          - contains: ["content.message", "账号不存在或已注销"]

- test:
      name: 流程归档-归档目录为空
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuIdList: ""
          processIdList:
              - $processId
      api: api/standing_book2.0_api/grouping_files2.0.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "归档分类不能为空"]

#- test:
#      name: 流程归档-归档目录不存在
#      variables:
#          accountId: ${ENV(ouid)}
#          tenantId: ${ENV(orgid2)}
#          menuIdList: "xxxxnoixtmenuid"
#          processIdList:
#            - $processId
#      api: api/standing_book2.0_api/grouping_files2.0.yml
#      validate:
#          - eq: ["content.code", ********]
#          - contains: ["content.message", "归档分类不能为空"]
#
#- test:
#      name: 流程归档-租户id为空
#      variables:
#          accountId: ${ENV(ouid)}
#          tenantId: ""
#          menuIdList: $menuId3
#          processIdList:
#             - $processId
#      api: api/standing_book2.0_api/grouping_files2.0.yml
#      validate:
#          - eq: ["content.code", ********]
#          - contains: ["content.message", "归档分类不能为空"]

- test:
      name: 流程归档-租户id不存在
      variables:
          accountId: ${ENV(ouid)}
          tenantId: "xxxxxnoixttenantid"
          menuIdList: $menuId3
          processIdList:
            - $processId
      api: api/standing_book2.0_api/grouping_files2.0.yml
      validate:
          - eq: ["content.code", *********]
          - contains: ["content.message", "账号不存在或已注销"]

#- test:
#      name: 流程归档-非当前企业下的归档目录
#      variables:
#          accountId: ${ENV(ouid)}
#          tenantId: ${ENV(orgid)}
#          menuIdList: $menuId3
#          processIdList: $processId
#      api: api/standing_book2.0_api/grouping_files2.0.yml
#      validate:
#          - eq: ["content.code", $code]
#          - eq: ["content.message", $message]

- test:
      name: 流程归档-正常归档单个流程
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuIdList: $menuId3
          processIdList:
            - $processId
      api: api/standing_book2.0_api/grouping_files2.0.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

- test:
      name: 流程移出分类
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          processId: $processId
          accountId2: ${ENV(ouid)}
          targetMenuId: $menuId3
      api: api/standing_book2.0_api/grouping_files2.0_remove.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

- test:
      name: 流程归档-正常归档多个流程
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuIdList:
             $menuId3
          processIdList:
              - $processId
              - $processId2
      api: api/standing_book2.0_api/grouping_files2.0.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

- test:
      name: 流程移出分类
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          processId: $processId
          accountId2: ${ENV(ouid)}
          targetMenuId: $menuId3
      api: api/standing_book2.0_api/grouping_files2.0_remove.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

- test:
      name: 流程移出分类
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          processId: $processId2
          accountId2: ${ENV(ouid)}
          targetMenuId: $menuId3
      api: api/standing_book2.0_api/grouping_files2.0_remove.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]