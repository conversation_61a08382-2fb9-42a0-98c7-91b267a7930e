- config:
    name: 智能台账2.0-企业合同列表
    base_url: ${ENV(contract_manager_url)}
    variables:
        - code: 0
        - message: 成功
        - ning: '[{"key":"title","value":["青莲"],"sort":""}]'

- test:
      name: 查询分类列表-获取可绑定分类id
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          ruleId: ""
      api: api/standing_book2.0_api/list_menu.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - CTmenuId: content.data.0.menuId
          - CTmenuId2: content.data.0.childNode.0.menuId
          - CTmenuId3: content.data.1.menuId

- test:
      name: 查询待归档合同列表-合同主题模糊搜索
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuId: ""
          pageSize: 10
          pageNum: 1
          matching: '[{"key":"title","value":["解析"],"sort":""}]'
      api: api/standing_book2.0_api/grouping_list.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
          - contains: ["content.data.groupingProcessList.0.title", "解析"]

- test:
      name: 查询待归档合同列表-单个合同状态搜索
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuId: ""
          pageSize: 10
          pageNum: 1
          matching: '[{"key":"title","value":[""],"sort":""},{"key":"processStatus","value":["2"],"sort":""}]'
      api: api/standing_book2.0_api/grouping_list.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
          - eq: ["content.data.groupingProcessList.0.processStatus", 2]

- test:
      name: 查询待归档合同列表-多个合同状态搜索
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuId: ""
          pageSize: 10
          pageNum: 1
          matching: '[{"key":"title","value":[""],"sort":""},{"key":"processStatus","value":["8","1"],"sort":""}]'
      api: api/standing_book2.0_api/grouping_list.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
          - eq: ["content.data.groupingProcessList.0.appName", "e签宝标准签"]

- test:
      name: 查询待归档合同列表-合同完成时间搜索-开区间时间搜索1
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuId: ""
          pageSize: 10
          pageNum: 1
          matching: '[{"key":"title","value":[""],"sort":""},{"key":"processStatus","value":[""],"sort":""},{"key":"completeTime","value":["*************",""],"sort":""}]'
      api: api/standing_book2.0_api/grouping_list.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

- test:
      name: 查询待归档合同列表-合同完成时间搜索-开区间时间搜索2
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuId: ""
          pageSize: 10
          pageNum: 1
          matching: '[{"key":"title","value":[""],"sort":""},{"key":"processStatus","value":[""],"sort":""},{"key":"completeTime","value":["","*************"],"sort":""}]'
      api: api/standing_book2.0_api/grouping_list.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]


- test:
      name: 查询待归档合同列表-发起人模糊查询
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuId: ""
          pageSize: 10
          pageNum: 1
          matching: '[{"key":"processAccountList","value":["玉华"],"sort":""}]'
      api: api/standing_book2.0_api/grouping_list.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
          #- contains: ["content.data.groupingProcessList.0.appName", "标准"]

- test:
      name: 查询待归档合同列表-发起人&参与人模糊查询
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuId: ""
          pageSize: 10
          pageNum: 1
          matching: '[{"key":"processAccountList","value":["宁"],"sort":""},{"key":"participantAccountLi","value":["玉华"],"sort":""}]'
      api: api/standing_book2.0_api/grouping_list.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
          - contains: ["content.data.groupingProcessList.0.appName", "标准"]

- test:
      name: 归档合同列表-合同主题模糊查询
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuId: $CTmenuId
          pageSize: 10
          pageNum: 1
          matching: '[{"key":"title","value":["1"],"sort":""}]'
      api: api/standing_book2.0_api/grouping_list.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
          - contains: ["content.data.groupingProcessList.0.appName", "标准"]

- test:
      name: 归档合同列表-合同主题升序排序
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuId: $CTmenuId
          pageSize: 10
          pageNum: 1
          matching: '[{"key":"title","value":"","sort":"asc"}]'
      api: api/standing_book2.0_api/grouping_list.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
          - contains: ["content.data.groupingProcessList.0.appName", "标准"]

- test:
      name: 归档合同列表-合同主题降序排序
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuId: $CTmenuId
          pageSize: 10
          pageNum: 1
          matching: '[{"key":"title","value":"","sort":"decs"}]'
      api: api/standing_book2.0_api/grouping_list.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
          - contains: ["content.data.groupingProcessList.0.appName", "标准"]

- test:
      name: 归档合同列表-合同编号精确搜索
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuId: $CTmenuId
          pageSize: 10
          pageNum: 1
          matching: '[{"key":"contractNo","value":["1255173286661347798"],"sort":""}]'
      api: api/standing_book2.0_api/grouping_list.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

- test:
      name: 归档合同列表-发起时间开区间降序搜索
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuId: $CTmenuId
          pageSize: 10
          pageNum: 1
          matching: '[{"key":"contractNo","value":[""],"sort":""},{"key":"processCreateTime","value":["*************",""],"sort":"decs"}]'
      api: api/standing_book2.0_api/grouping_list.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
