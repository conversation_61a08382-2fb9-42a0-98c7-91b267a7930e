- config:
    name: 查询台账规则模板详情(场景测试)
    base_url: ${ENV(contract_manager_url)}
    variables:
        - code: 0
        - message: 成功
        - name: list-${getTimeStamp()}

- test:
      name: 上传台账规则模板(流程模板)-获取规则id
      variables:
        accountId: ${ENV(ouid)}
        tenantId: ${ENV(orgid2)}
        fileId: ""
        ruleTemplateId: null
        flowTemplateId: ${ENV(flowTemplateId1)}
      api: api/standing_book2.0_api/create_templaterule_id.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - ruleTemplateId: content.data

- test:
    name: 创建归档目录-获取menuId
    api: api/groupingdir/creategrouping_menus.yml
    variables:
      accountId1: ${ENV(ouid)}
      createTenantId: ${ENV(orgid2)}
      parentMenuId:
      name: "list_detail-${getTimeStamp()}"
    extract:
        - menuId: content.data
    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]


- test:
      name: 新增台账规则
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: ""
          - menuList: $menuId
          - name: $name
          - remark: "青莲在测试-查询台账规则模板详情"
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                "ruleTemplateMenuId": $menuId
                "ruleTemplateId": $ruleTemplateId

      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", $code]
          - contains: ["content.message", $message]
      extract:
          - ruleId: content.data.ruleId

- test:
    name: 查询台账规则模板详情-accountId为空
    api: api/standing_book2.0_api/list_template_detail.yml
    variables:
        ruleId: $ruleId
        ruleTemplateId: $ruleTemplateId
        accountId: ""
        tenantId: ${ENV(orgid2)}
    validate:
        - eq: ["content.code", ********]
        - contains: ["content.message", "无操作权限"]

- test:
    name: 查询台账规则模板详情-accountId不存在
    api: api/standing_book2.0_api/list_template_detail.yml
    variables:
        ruleId: $ruleId
        ruleTemplateId: $ruleTemplateId
        accountId: "xxxxnoexist"
        tenantId: ${ENV(orgid2)}
    validate:
        - eq: ["content.code", ********]
        - contains: ["content.message", "无操作权限"]

- test:
    name: 查询台账规则模板详情-accountId为企业oid
    api: api/standing_book2.0_api/list_template_detail.yml
    variables:
        ruleId: $ruleId
        ruleTemplateId: $ruleTemplateId
        accountId: ${ENV(orgid2)}
        tenantId: ${ENV(orgid2)}
    validate:
        - eq: ["content.code", ********]
        - contains: ["content.message", "无操作权限"]

- test:
    name: 查询台账规则模板详情-租户id为空
    api: api/standing_book2.0_api/list_template_detail.yml
    variables:
        ruleId: $ruleId
        ruleTemplateId: $ruleTemplateId
        accountId: ${ENV(ouid)}
        tenantId: ""
    validate:
        - eq: ["content.code", ********]
        - contains: ["content.message", "缺少参数: orgId"]

- test:
    name: 查询台账规则模板详情-accountId与租户id不匹配
    api: api/standing_book2.0_api/list_template_detail.yml
    variables:
        ruleId: $ruleId
        ruleTemplateId: $ruleTemplateId
        accountId: ${ENV(accountId)}
        tenantId: ${ENV(orgid2)}
    validate:
        - eq: ["content.code", ********]
        - contains: ["content.message", "无操作权限"]

- test:
    name: 查询台账规则模板详情-租户id为空
    api: api/standing_book2.0_api/list_template_detail.yml
    variables:
        ruleId: $ruleId
        ruleTemplateId: $ruleTemplateId
        accountId: ${ENV(ouid)}
        tenantId: ""
    validate:
        - eq: ["content.code", ********]
        - contains: ["content.message", "缺少参数: orgId"]

- test:
    name: 查询台账规则模板详情-ruleId为空
    api: api/standing_book2.0_api/list_template_detail.yml
    variables:
        ruleId: ""
        ruleTemplateId: $ruleTemplateId
        accountId: ${ENV(ouid)}
        tenantId: ${ENV(orgid2)}
    validate:
        - eq: ["content.code", $code]
        - contains: ["content.message", $message]

- test:
    name: 查询台账规则模板详情-ruleId不存在
    api: api/standing_book2.0_api/list_template_detail.yml
    variables:
        ruleId: "xxxxxnoixueyu"
        ruleTemplateId: $ruleTemplateId
        accountId: ${ENV(ouid)}
        tenantId: ${ENV(orgid2)}
    validate:
        - eq: ["content.code", $code]
        - contains: ["content.message", $message]

- test:
    name: 查询台账规则模板详情-非当前企业下创建的ruleId
    api: api/standing_book2.0_api/list_template_detail.yml
    variables:
        ruleId: $ruleId
        ruleTemplateId: $ruleTemplateId
        accountId: ${ENV(ouid)}
        tenantId: ${ENV(orgid)}
    validate:
        - eq: ["content.code", ********]
        - contains: ["content.message", "台账规则模板不存在"]

- test:
    name: 查询台账规则模板详情-ruleTemplateId为空
    api: api/standing_book2.0_api/list_template_detail.yml
    variables:
        ruleId: $ruleId
        ruleTemplateId: ""
        accountId: ${ENV(ouid)}
        tenantId: ${ENV(orgid2)}
    validate:
        - eq: ["content.code", $code]
        - contains: ["content.message", $message]

- test:
    name: 查询台账规则模板详情-ruleTemplateId不存在
    api: api/standing_book2.0_api/list_template_detail.yml
    variables:
        ruleId: $ruleId
        ruleTemplateId: "xxxxiugiugiuewuiw"
        accountId: ${ENV(ouid)}
        tenantId: ${ENV(orgid2)}
    validate:
        - eq: ["content.code", $code]
        - contains: ["content.message", $message]

- test:
    name: 查询台账规则模板详情-非当前企业下创建的ruleTemplateId
    api: api/standing_book2.0_api/list_template_detail.yml
    variables:
        ruleId: $ruleId
        ruleTemplateId: $ruleTemplateId
        accountId: ${ENV(ouid)}
        tenantId: ${ENV(orgid)}
    validate:
        - eq: ["content.code", ********]
        - contains: ["content.message", "台账规则模板不存在"]

- test:
    name: 查询台账规则模板详情-正常查询模板详情
    api: api/standing_book2.0_api/list_template_detail.yml
    variables:
        ruleId: $ruleId
        ruleTemplateId: $ruleTemplateId
        accountId: ${ENV(ouid)}
        tenantId: ${ENV(orgid2)}
    validate:
        - eq: ["content.code", $code]
        - contains: ["content.message", $message]
        - contains: ["content.data.structList.0.content", "孔明"]


- test:
      name: 删除新增的台账规则
      variables:
          - ruleId: $ruleId
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
      api: api/standing_book2.0_api/delete_rule.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

- test:
    name: 删除归档目录-menuId
    api: api/groupingdir/deletegrouping_menus.yml
    variables:
      menuId: $menuId
      accountId2: ${ENV(ouid)}
      deleteTenantId: ${ENV(orgid2)}
    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]