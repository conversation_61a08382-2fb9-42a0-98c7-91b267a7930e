- config:
    name: 智能台账2.0-移动流程分类
    base_url: ${ENV(contract_manager_url)}
    variables:
        - code: 0
        - message: 成功

- test:
      name: 查询分类列表-获取可绑定分类id
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          ruleId: ""
      api: api/standing_book2.0_api/list_menu.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - menuId: content.data.0.menuId
          - menuId2: content.data.0.childNode.0.menuId
          - menuId3: content.data.1.menuId

- test:
      name: 待归档列表查询
      variables:
          unquerylistoid: ${ENV(ouid)}
          unquerylistTenantId: ${ENV(orgid2)}
          menuId: ""
          title: ""
          initiator: ""
          createFrom: ""
          createEnd: ""
      api: api/groupingquery/ungroupinglistquery.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - processId: content.data.groupingProcessList.0.processId
          - processId2: content.data.groupingProcessList.1.processId

- test:
      name: 流程归档
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          menuIdList: $menuId3
          processIdList:
            - $processId
      api: api/standing_book2.0_api/grouping_files2.0.yml
      validate:
          - eq: ["content.code", $code]
          - contains: ["content.message", $message]

- test:
      name: 移动流程分类-accountId为空
      variables:
          accountId: ""
          tenantId: ${ENV(orgid2)}
          processId: $processId
          accountId2: ${ENV(ouid)}
          sourceMenuId: $menuId3
          targetMenuIdList:
            - $menuId
      api: api/standing_book2.0_api/grouping_files2.0_moveMenu.yml
      validate:
          - eq: ["content.code", $code]
          - contains: ["content.message", $message]

- test:
      name: 移动流程分类-accountId不存在
      variables:
          accountId: "xxxxxxxnoexist"
          tenantId: ${ENV(orgid2)}
          processId: $processId
          accountId2: ${ENV(ouid)}
          sourceMenuId: $menuId3
          targetMenuIdList:
            - $menuId
      api: api/standing_book2.0_api/grouping_files2.0_moveMenu.yml
      validate:
          - eq: ["content.code", $code]
          - contains: ["content.message", $message]

#- test:
#      name: 移动流程分类-租户id为空
#      variables:
#          accountId: ${ENV(ouid)}
#          tenantId: ""
#          processId: $processId
#          accountId2: ${ENV(ouid)}
#          sourceMenuId: $menuId3
#          targetMenuIdList:
#            - $menuId
#      api: api/standing_book2.0_api/grouping_files2.0_moveMenu.yml
#      validate:
#          - eq: ["content.code", $code]
#          - contains: ["content.message", $message]

- test:
      name: 移动流程分类-租户id不存在
      variables:
          accountId: ${ENV(ouid)}
          tenantId: "xxxxxnoexist"
          processId: $processId
          accountId2: ${ENV(ouid)}
          sourceMenuId: $menuId3
          targetMenuIdList:
            - $menuId
      api: api/standing_book2.0_api/grouping_files2.0_moveMenu.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "无操作权限"]

- test:
      name: 移动流程分类-processId为空
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          processId: ""
          accountId2: ${ENV(ouid)}
          sourceMenuId: $menuId3
          targetMenuIdList:
            - $menuId
      api: api/standing_book2.0_api/grouping_files2.0_moveMenu.yml
      validate:
          - eq: ["content.code", $code]
          - contains: ["content.message", $message]

- test:
      name: 移动流程分类-processId不存在
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          processId: "xxxxnoexistprocessid"
          accountId2: ${ENV(ouid)}
          sourceMenuId: $menuId3
          targetMenuIdList:
            - $menuId
      api: api/standing_book2.0_api/grouping_files2.0_moveMenu.yml
      validate:
          - eq: ["content.code", $code]
          - contains: ["content.message", $message]

- test:
      name: 移动流程分类-非当前企业下的processId
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid)}
          processId: $processId
          accountId2: ${ENV(ouid)}
          sourceMenuId: $menuId3
          targetMenuIdList:
            - $menuId
      api: api/standing_book2.0_api/grouping_files2.0_moveMenu.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "无操作权限"]

#- test:
#      name: 移动流程分类-sourceMenuId为空
#      variables:
#          accountId: ${ENV(ouid)}
#          tenantId: ${ENV(orgid)}
#          processId: $processId
#          accountId2: ${ENV(ouid)}
#          sourceMenuId: ""
#          targetMenuIdList:
#            - $menuId
#      api: api/standing_book2.0_api/grouping_files2.0_moveMenu.yml
#      validate:
#          - eq: ["content.code", $code]
#          - contains: ["content.message", $message]

- test:
      name: 移动流程分类-sourceMenuId不存在
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid)}
          processId: $processId
          accountId2: ${ENV(ouid)}
          sourceMenuId: "xxxxnoexistmenuid"
          targetMenuIdList:
            - $menuId
      api: api/standing_book2.0_api/grouping_files2.0_moveMenu.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "无操作权限"]

- test:
      name: 移动流程分类-targetMenuIdList为空
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid)}
          processId: $processId
          accountId2: ${ENV(ouid)}
          sourceMenuId: $menuId3
          targetMenuIdList:
            - ""
      api: api/standing_book2.0_api/grouping_files2.0_moveMenu.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "无操作权限"]

#- test:
#      name: 移动流程分类-targetMenuIdList不存在
#      variables:
#          accountId: ${ENV(ouid)}
#          tenantId: ${ENV(orgid)}
#          processId: $processId
#          accountId2: ${ENV(ouid)}
#          sourceMenuId: $menuId3
#          targetMenuIdList:
#            - "xxxxxnoisewdqw"
#      api: api/standing_book2.0_api/grouping_files2.0_moveMenu.yml
#      validate:
#          - eq: ["content.code", ********]
#          - contains: ["content.message", "归档分类不能为空"]

- test:
      name: 正常移动流程分类
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          processId: $processId
          accountId2: ${ENV(ouid)}
          sourceMenuId: $menuId3
          targetMenuIdList:
            - $menuId
      api: api/standing_book2.0_api/grouping_files2.0_moveMenu.yml
      validate:
          - eq: ["content.code", $code]
          - contains: ["content.message", $message]

- test:
      name: 流程移出分类
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          processId: $processId2
          accountId2: ${ENV(ouid)}
          targetMenuId: $menuId
      api: api/standing_book2.0_api/grouping_files2.0_remove.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]