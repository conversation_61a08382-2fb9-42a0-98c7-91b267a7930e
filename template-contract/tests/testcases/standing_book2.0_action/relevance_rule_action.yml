- config:
    name: 关联模板字段
    base_url: ${ENV(contract_manager_url)}
    variables:
        - code: 0
        - message: 成功
        - name1: relerule-${getTimeStamp()}


- test:
      name: 上传台账规则模板-获取规则id
      variables:
        accountId: ${ENV(ouid)}
        tenantId: ${ENV(orgid2)}
        fileId: ""
        ruleTemplateId: null
        flowTemplateId: ${ENV(flowTemplateId2)}
      api: api/standing_book2.0_api/create_templaterule_id.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - ruleTemplateId: content.data

- test:
    name: 创建归档目录-获取menuId
    api: api/groupingdir/creategrouping_menus.yml
    variables:
      accountId1: ${ENV(ouid)}
      createTenantId: ${ENV(orgid2)}
      parentMenuId:
      name: menu-${getTimeStamp()}
    teardown_hooks:
        - ${teardown_hook_sleep_N_secs(1)}
    extract:
        - menuId: content.data
    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]

- test:
      name: 新增台账规则
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: ""
          - menuList: $menuId
          - name: $name1
          - remark: "青莲在测试"
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                "ruleTemplateMenuId": $menuId
                "ruleTemplateId": $ruleTemplateId

      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", $code]
          - contains: ["content.message", $message]
      extract:
        - ruleId: content.data.ruleId

- test:
      name: 查询规则详情-获取字段Id
      variables:
          - ruleId: $ruleId
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
      api: api/standing_book2.0_api/list_rule_detail.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - fieldId: content.data.ruleFieldList.0.fieldId

- test:
      name: 查询台账规则模板详情-获取控件Id
      variables:
          - ruleId: $ruleId
          - ruleTemplateId: $ruleTemplateId
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
      api: api/standing_book2.0_api/list_template_detail.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - structId: content.data.structList.0.structId

- test:
      name: 关联模板字段-accountId为空
      variables:
          - accountId: ""
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - ruleTemplateList:
                ruleConfigList:
                    - structId: $structId
                      fieldId: $fieldId
                      structType: 1
                ruleTemplateId: $ruleTemplateId

      api: api/standing_book2.0_api/relevance_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "无操作权限"]

- test:
      name: 关联模板字段-accountId不存在
      variables:
          - accountId: "xxxxuuqoifhqciio"
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - ruleTemplateList:
               ruleConfigList:
                  - structId: $structId
                    fieldId: $fieldId
                    structType: 1
               ruleTemplateId: $ruleTemplateId

      api: api/standing_book2.0_api/relevance_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "无操作权限"]

- test:
      name: 关联模板字段-租户id不存在
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(ouid)}
          - ruleId: $ruleId
          - ruleTemplateList:
               ruleConfigList:
                  - structId: $structId
                    fieldId: $fieldId
                    structType: 1
               ruleTemplateId: $ruleTemplateId

      api: api/standing_book2.0_api/relevance_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "组织架构不存在"]

- test:
      name: 关联模板字段-ruleId为空
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: ""
          - ruleTemplateList:
               ruleConfigList:
                  - structId: $structId
                    fieldId: $fieldId
                    structType: 1
               ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/relevance_rule.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

- test:
      name: 关联模板字段-ruleId不存在
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: "xxxxrule234g"
          - ruleTemplateList:
               ruleConfigList:
                  - structId: $structId
                    fieldId: $fieldId
                    structType: 1
               ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/relevance_rule.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

- test:
      name: 关联模板字段-非当前企业创建的ruleId
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: ${ENV(ruleId_other)}
          - ruleTemplateList:
               ruleConfigList:
                  - structId: $structId
                    fieldId: $fieldId
                    structType: 1
               ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/relevance_rule.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

#- test:
#      name: 关联模板字段-ruleId已经被删除
#      variables:
#          - accountId: ${ENV(ouid)}
#          - tenantId: ${ENV(orgid2)}
#          - ruleId: ${ENV(ruleId_delete)}
#          - ruleTemplateList:
#              - ruleConfigList:
#                  - structId: $structId
#                    fieldId: $fieldId
#                    structType: 1
#                ruleTemplateId: $ruleTemplateId
#      api: api/standing_book2.0_api/relevance_rule.yml
#      validate:
#          - eq: ["content.code", $code]
#          - eq: ["content.message", $message]

- test:
      name: 关联模板字段-ruleTemplateId为空
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - ruleTemplateList:
               ruleConfigList:
                  - structId: $structId
                    fieldId: $fieldId
                    structType: 1
               ruleTemplateId: ""
      api: api/standing_book2.0_api/relevance_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "参数错误"]

- test:
      name: 关联模板字段-ruleTemplateId不存在
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: $ruleId
          - ruleTemplateList:
               ruleConfigList:
                  - structId: $structId
                    fieldId: $fieldId
                    structType: 1
               ruleTemplateId: "xxxxwgjg2998998"
      api: api/standing_book2.0_api/relevance_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "台账规则模板不存在"]

#- test:
#      name: 关联模板字段-structId为空
#      variables:
#          - accountId: ${ENV(ouid)}
#          - tenantId: ${ENV(orgid2)}
#          - ruleId: $ruleId
#          - ruleTemplateList:
#               ruleConfigList:
#                  - structId: ""
#                    fieldId: $fieldId
#                    structType: 1
#               ruleTemplateId: $ruleTemplateId
#      api: api/standing_book2.0_api/relevance_rule.yml
#      validate:
#          - eq: ["content.code", $code]
#          - eq: ["content.message", $message]
#
#- test:
#      name: 关联模板字段-structId不存在
#      variables:
#          - accountId: ${ENV(ouid)}
#          - tenantId: ${ENV(orgid2)}
#          - ruleId: $ruleId
#          - ruleTemplateList:
#               ruleConfigList:
#                  - structId: "xxxxstrucId386298"
#                    fieldId: $fieldId
#                    structType: 1
#               ruleTemplateId: $ruleTemplateId
#      api: api/standing_book2.0_api/relevance_rule.yml
#      validate:
#          - eq: ["content.code", $code]
#          - eq: ["content.message", $message]
#
#- test:
#      name: 关联模板字段-fieldId为空
#      variables:
#          - accountId: ${ENV(ouid)}
#          - tenantId: ${ENV(orgid2)}
#          - ruleId: $ruleId
#          - ruleTemplateList:
#                ruleConfigList:
#                  - structId: $structId
#                    fieldId: ""
#                    structType: 1
#                ruleTemplateId: $ruleTemplateId
#      api: api/standing_book2.0_api/relevance_rule.yml
#      validate:
#          - eq: ["content.code", $code]
#          - eq: ["content.message", $message]
#
#- test:
#      name: 关联模板字段-fieldId不存在
#      variables:
#          - accountId: ${ENV(ouid)}
#          - tenantId: ${ENV(orgid2)}
#          - ruleId: $ruleId
#          - ruleTemplateList:
#                ruleConfigList:
#                  - structId: $structId
#                    fieldId: "xxxnoexitfileIdid"
#                    structType: 1
#                ruleTemplateId: $ruleTemplateId
#      api: api/standing_book2.0_api/relevance_rule.yml
#      validate:
#          - eq: ["content.code", $code]
#          - eq: ["content.message", $message]

#- test:
#      name: 关联模板字段-structType非1或2的其他值
#      variables:
#          - accountId: ${ENV(ouid)}
#          - tenantId: ${ENV(orgid2)}
#          - ruleId: $ruleId
#          - ruleTemplateList:
#               ruleConfigList:
#                  - structId: $structId
#                    fieldId: $fieldId
#                    structType: 574
#               ruleTemplateId: $ruleTemplateId
#      api: api/standing_book2.0_api/relevance_rule.yml
#      validate:
#          - eq: ["content.code", $code]
#          - eq: ["content.message", $message]

- test:
      name: 查询规则详情-规则是生效状态
      variables:
          - ruleId: $ruleId
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
      api: api/standing_book2.0_api/list_rule_detail.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
          - eq: ["content.data.ruleId", $ruleId]

- test:
      name: 删除新增的台账规则
      variables:
          - ruleId: $ruleId
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
      api: api/standing_book2.0_api/delete_rule.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]

- test:
    name: 删除归档目录
    api: api/groupingdir/deletegrouping_menus.yml
    variables:
      menuId: $menuId
      accountId2: ${ENV(ouid)}
      deleteTenantId: ${ENV(orgid2)}
    validate:
        - eq: ["content.code", $code]
        - eq: ["content.message", $message]