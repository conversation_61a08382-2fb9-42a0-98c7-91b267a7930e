- config:
    name: 保存台账规则
    base_url: ${ENV(contract_manager_url)}
    variables:
        - code: 0
        - message: 成功
        - name: rule-${getTimeStamp()}



- test:
      name: 上传台账规则模板-获取规则id
      variables:
        accountId: ${ENV(ouid)}
        tenantId: ${ENV(orgid2)}
        fileId: ""
        ruleTemplateId: null
        flowTemplateId: ${ENV(flowTemplateId1)}
      api: api/standing_book2.0_api/create_templaterule_id.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - ruleTemplateId: content.data

- test:
      name: 查询分类列表-获取可绑定分类id
      variables:
          accountId: ${ENV(ouid)}
          tenantId: ${ENV(orgid2)}
          ruleId: ""
      api: api/standing_book2.0_api/list_menu.yml
      validate:
          - eq: ["content.code", $code]
          - eq: ["content.message", $message]
      extract:
          - menuId: content.data.0.menuId
          - menuId2: content.data.0.childNode.0.menuId
          - menuId3: content.data.1.menuId


- test:
      name: 新增台账规则-accountId为空
      variables:
          - accountId: ""
          - tenantId: ${ENV(orgid2)}
          - ruleId: ""
          - menuList: $menuId
          - name: $name
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                "ruleTemplateMenuId": $menuId
                "ruleTemplateId": $ruleTemplateId

      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "无操作权限"]

- test:
      name: accountId与tenantId不匹配
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(ouid)}
          - ruleId: ""
          - menuList: $menuId
          - name: $name
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: $menuId
                ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "组织架构不存在"]

##- test:
##      name: 新增台账规则-name长度超过20字符
##      variables:
##          - accountId: ${ENV(ouid)}
##          - tenantId: ${ENV(orgid2)}
##          - ruleId: ""
##          - menuList: $menuId
##          - name: "name长度超过20字符name长度超过20字符"
##          - remark: ""
##          - ruleFieldList:
##              - fieldId: ""
##                fieldName: "姓名"
##                fieldType: 1
##          - ruleTemplateMenuList:
##              - ruleTemplateMenuId: $menuId,
##                ruleTemplateId: $ruleTemplateId
##      api: api/standing_book2.0_api/save_rule.yml
##      validate:
##          - eq: ["content.code", $code]
##          - contains: ["content.message", $message]
##
##- test:
##      name: 新增台账规则-remark长度超过50字符
##      variables:
##          - accountId: ${ENV(ouid)}
##          - tenantId: ${ENV(orgid2)}
##          - ruleId: ""
##          - menuList: ""
##          - name: $name
##          - remark: "新增一个台账规则20新增一个台账规则20新增一个台账规则20新增一个台账规则20新增一个台账规则20x"
##          - ruleFieldList:
##              - fieldId: ""
##                fieldName: "姓名"
##                fieldType: ""
##          - ruleTemplateMenuList:
##              - ruleTemplateMenuId: "33",
##                ruleTemplateId: $ruleTemplateId
##      api: api/standing_book2.0_api/save_rule.yml
##      validate:
##          - eq: ["content.code", $code]
##          - eq: ["content.message", $message]
#
- test:
      name: 新增台账规则-menuList为空
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: ""
          - menuList: ""
          - name: $name
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: $menuId
                ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "分类不存在"]

- test:
      name: 新增台账规则-分类id非一级分类id
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: ""
          - menuList: $menuId2
          - name: $name
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: $menuId
                ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "台账规则只能绑定一级分类"]

#- test:
#      name: 新增台账规则-当前分类被其他台账规则关联
#      variables:
#          - accountId: ${ENV(ouid)}
#          - tenantId: ${ENV(orgid2)}
#          - ruleId: ""
#          - menuList: ""
#          - name: $name
#          - remark: ""
#          - ruleFieldList:
#              - fieldId: ""
#                fieldName: "姓名"
#                fieldType: ""
#          - ruleTemplateMenuList:
#              - ruleTemplateMenuId: "33",
#                ruleTemplateId: $ruleTemplateId
#      api: api/standing_book2.0_api/save_rule.yml
#      validate:
#          - eq: ["content.code", $code]
#          - eq: ["content.message", $message]
#
- test:
      name: 当前分类被删除或被移动成二级或三级分类
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: ""
          - menuList: ${ENV(menuId_delete)}
          - name: $name
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: ""
          - ruleTemplateMenuList:
                ruleTemplateMenuId: $menuId
                ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "分类不存在"]

#- test:
#      name: 新增台账规则-表头扩展字段为空
#      variables:
#          - accountId: ${ENV(ouid)}
#          - tenantId: ${ENV(orgid2)}
#          - ruleId: ""
#          - menuList: ""
#          - name: $name
#          - remark: ""
#          - ruleFieldList:
#              - fieldId: ""
#                fieldName: "姓名"
#                fieldType: ""
#          - ruleTemplateMenuList:
#              - ruleTemplateMenuId: "33",
#                ruleTemplateId: $ruleTemplateId
#      api: api/standing_book2.0_api/save_rule.yml
#      validate:
#          - eq: ["content.code", $code]
#          - eq: ["content.message", $message]
#
#- test:
#      name: 新增台账规则-表头扩展字段长度大于20字符
#      variables:
#          - accountId: ${ENV(ouid)}
#          - tenantId: ${ENV(orgid2)}
#          - ruleId: ""
#          - menuList: ""
#          - name: $name
#          - remark: ""
#          - ruleFieldList:
#              - fieldId: ""
#                fieldName: "表头扩展字段长度大于20字符表头扩展字段长"
#                fieldType: ""
#          - ruleTemplateMenuList:
#              - ruleTemplateMenuId: "33",
#                ruleTemplateId: $ruleTemplateId
#      api: api/standing_book2.0_api/save_rule.yml
#      validate:
#          - eq: ["content.code", $code]
#          - eq: ["content.message", $message]
#
#- test:
#      name: 新增台账规则-表头扩展字段为空
#      variables:
#          - accountId: ${ENV(ouid)}
#          - tenantId: ${ENV(orgid2)}
#          - ruleId: ""
#          - menuList: ""
#          - name: $name
#          - remark: ""
#          - ruleFieldList:
#              - fieldId: ""
#                fieldName: ""
#                fieldType: ""
#          - ruleTemplateMenuList:
#              - ruleTemplateMenuId: "33",
#                ruleTemplateId: $ruleTemplateId
#      api: api/standing_book2.0_api/save_rule.yml
#      validate:
#          - eq: ["content.code", $code]
#          - eq: ["content.message", $message]
#
- test:
      name: 新增台账规则-模板所属分类为空
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: ""
          - menuList: $menuId
          - name: $name
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: ""
                ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "分类不存在"]

- test:
      name: 新增台账规则-模板所属分类不存在
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: ""
          - menuList: $menuId
          - name: $name
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: "2523xxxwqrwqtweye214"
                ruleTemplateId: $ruleTemplateId
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - contains: ["content.message", "分类不存在"]

#- test:
#      name: 新增台账规则-非当前所选分类或其子分类
#      variables:
#          - accountId: ${ENV(ouid)}
#          - tenantId: ${ENV(orgid2)}
#          - ruleId: ""
#          - menuList: $menuId
#          - name: $name
#          - remark: ""
#          - ruleFieldList:
#                fieldId: ""
#                fieldName: "姓名"
#                fieldType: 1
#          - ruleTemplateMenuList:
#                ruleTemplateMenuId: $menuId3
#                ruleTemplateId: $ruleTemplateId
#      api: api/standing_book2.0_api/save_rule.yml
#      validate:
#          - eq: ["content.code", ********]
#          - eq: ["content.message", "分类不存在"]

- test:
      name: 新增台账规则-模板id为空
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: ""
          - menuList: $menuId
          - name: $name
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: $menuId
                ruleTemplateId: ""
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - eq: ["content.message", "无操作权限"]

- test:
      name: 新增台账规则-模板id不存在
      variables:
          - accountId: ${ENV(ouid)}
          - tenantId: ${ENV(orgid2)}
          - ruleId: ""
          - menuList: $menuId
          - name: $name
          - remark: ""
          - ruleFieldList:
                fieldId: ""
                fieldName: "姓名"
                fieldType: 1
          - ruleTemplateMenuList:
                ruleTemplateMenuId: $menuId
                ruleTemplateId: "xxxxugdweidugi214525"
      api: api/standing_book2.0_api/save_rule.yml
      validate:
          - eq: ["content.code", ********]
          - eq: ["content.message", "无操作权限"]