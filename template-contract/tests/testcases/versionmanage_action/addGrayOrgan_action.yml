- config:
    name: 新增灰度企业
    base_url: ${ENV(gray_manage_url)}
    variables:
        - projectKey: mx_test
        - sql1: "update gray_version set history=1 where type=1 and history=0 and proj_key='$projectKey';"
        - sql2: "update gray_user_version set deleted=1 where user_id='$orgId' and deleted=0 and version_key like 'mx_test_%';"
        - sql3: "update gray_version set history=1 where type=0 and history=0 and proj_key='$projectKey';"
        - orgId: ${ENV(gray_organ_gid)}
        - orgName: 杭州市上城区金宏洲包子铺
        - managerName: 明绣



- test:
    name: 新增灰度企业-userId为空
    api: api/version_manage/add_gray_organ.yml
    variables:
        - userId: ''
    validate:
        - eq: ["content.code", 20000002]
        - contains: ["content.message", 企业gid不能为空]


- test:
    name: 新增灰度企业-userId不存在
    api: api/version_manage/add_gray_organ.yml
    variables:
        - userId: 123
    validate:
        - eq: ["content.code", 20000004]
        - contains: ["content.message", 用户账号不存在]



- test:
      name: 新增灰度企业-userId为个人gid
      api: api/version_manage/add_gray_organ.yml
      variables:
          - userId: ${ENV(gray_user_gid)}
      validate:
          - eq: ["content.code", 20000201]
          - contains: ["content.message", 非企业账号， 不能新增灰度企业]


- test:
      name: 新增灰度企业-userName为空
      api: api/version_manage/add_gray_organ.yml
      variables:
          - userName: ''
      validate:
          - eq: ["content.code", 20000002]
          - contains: ["content.message", 企业名称不能为空]


- test:
      name: 新增灰度企业-标准签无灰度版本
      api: api/version_manage/add_gray_organ.yml
      validate:
          - eq: ["content.code", 20000103]
          - contains: ["content.message", 灰度版本不存在]
      setup_hooks:
        - ${hook_db_data($sql1)}
        - ${hook_db_data($sql2)}
        - ${hook_db_data($sql3)}


- test:
      name: 新增灰度版本
      api: api/version_manage/add_version.yml
      variables:
          - version: 9.9
          - versionType: 1
          - versionNote: 自动化测试
          - upgradable: false
      validate:
          - eq: ["content.code", 0]
          - contains: ["content.message", 成功]


- test:
      name: 新增灰度企业-成功
      api: api/version_manage/add_gray_organ.yml
      variables:
          - organName: $orgName
          - userId: $orgId
          - manager: $managerName
      validate:
          - eq: ["content.code", 0]
          - contains: ["content.message", 成功]


- test:
    name: 获取灰度企业列表-指定organName查询
    api: api/version_manage/gray_organ_list.yml
    variables:
      - organName: $orgName
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 获取灰度企业列表-指定organId查询
    api: api/version_manage/gray_organ_list.yml
    variables:
      - organId: $orgId
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]


- test:
    name: 获取灰度企业列表-指定manager查询
    api: api/version_manage/gray_organ_list.yml
    variables:
      - manager: $managerName
    validate:
      - eq: ["content.code", 0]
      - eq: ["content.message", 成功]
