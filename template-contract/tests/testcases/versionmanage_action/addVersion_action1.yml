- config:
    name: 新增版本
    base_url: ${ENV(gray_manage_url)}
    variables:
      - release_version: 1.1.0
      - projectKey: mx_test1
      - sql1: "update gray_version set history=1 where type=0 and history=0 and proj_key='$projectKey';"
      - sql2: "update gray_version set history=1 where type=1 and history=0 and proj_key='$projectKey';"
      - note: 自动化测试(新增版本)


- test:
    name: 新增正式版本
    api: api/version_manage/add_version.yml
    variables:
      - version: $release_version
      - versionType: 0
      - versionNote: $note
      - upgradable: false
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]
    extract:
      - vUuid: content.data
    setup_hooks:
      - ${hook_db_data($sql1)}
      - ${hook_db_data($sql2)}


- test:
    name: 新增灰度版本（版本号=正式版本号）
    api: api/version_manage/add_version.yml
    variables:
      - version: 1.1
      - versionType: 1
      - versionNote: $note
      - upgradable: true
    validate:
      - eq: ["content.code", 20000106]
      - contains: ["content.message", 版本号必须高于当前正式版本]


- test:
    name: 新增灰度版本（版本号>正式版本号）
    api: api/version_manage/add_version.yml
    variables:
      - version: 1.2
      - versionType: 1
      - versionNote: $note
      - upgradable: true
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]


- test:
    name: 新增灰度版本（已有灰度版本）
    api: api/version_manage/add_version.yml
    variables:
      - version: 1.3
      - versionType: 1
      - versionNote: $note
      - upgradable: true
    validate:
      - eq: ["content.code", 20000100]
      - contains: ["content.message", 已存在灰度版本]


- test:
    name: 新增正式版本（版本号=正式版本号）
    api: api/version_manage/add_version.yml
    variables:
      - version: $release_version
      - versionType: 0
      - versionNote: $note
      - upgradable: false
    validate:
      - eq: ["content.code", 20000106]
      - contains: ["content.message", 版本号必须高于当前正式版本]


- test:
    name: 新增正式版本（版本号=灰度版本号）
    api: api/version_manage/add_version.yml
    variables:
      - version: 1.2.0
      - versionType: 0
      - versionNote: $note
      - upgradable: false
    validate:
      - eq: ["content.code", 20000105]
      - contains: ["content.message", 版本号必须低于当前灰度版本]


- test:
    name: 新增正式版本（正式版本号<版本号<灰度版本号）
    api: api/version_manage/add_version.yml
    variables:
      - version: ${versionIncrement($release_version)}
      - versionType: 0
      - versionNote: $note
      - upgradable: false
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]


- test:
    name: 获取版本信息
    api: api/version_manage/version_info.yml
    variables:
      - versionUuid: $vUuid
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]
