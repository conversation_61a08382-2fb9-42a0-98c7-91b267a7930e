- config:
    name: 自助新增管理员管理的所有企业
    base_url: ${ENV(gray_manage_url)}
    variables:
      - projectKey: mx_test2
      - sql1: "update gray_version set history=1 where type=0 and history=0 and proj_key='$projectKey';"
      - sql2: "update gray_version set history=1 where type=1 and history=0 and proj_key='$projectKey';"


- test:
    name: 自助升级-userId为空
    api: api/version_manage/admin_upgrade_organ.yml
    variables:
      - userId: ''
    validate:
      - eq: ["content.code", 20000002]
      - contains: ["content.message", 用户oid不能为空]


- test:
    name: 自助升级-userId不存在
    api: api/version_manage/admin_upgrade_organ.yml
    variables:
      - userId: 123
    validate:
      - eq: ["content.code", 20000001]
      - contains: ["content.message", open user不存在]


- test:
    name: 自助升级-userId为企业oid
    api: api/version_manage/admin_upgrade_organ.yml
    variables:
      - userId: ${ENV(gray_organ_oid)}
    validate:
      - eq: ["content.code", 20000203]
      - contains: ["content.message", 自助升级仅支持管理员个人账号操作]


- test:
    name: 新增灰度版本
    api: api/version_manage/add_version.yml
    variables:
      - version: 9.9
      - versionType: 1
      - versionNote: 自动化测试
      - upgradable: false
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]
    setup_hooks:
      - ${hook_db_data($sql1)}
      - ${hook_db_data($sql2)}


- test:
    name: 自助升级-userId不是企业管理员
    api: api/version_manage/admin_upgrade_organ.yml
    variables:
      - userId: 18b62eb24ea140e9826583cf9b232892
    validate:
      - eq: ["content.code", 20000204]
      - contains: ["content.message", 当前用户不是企业管理员]


- test:
    name: 自助升级-成功
    api: api/version_manage/admin_upgrade_organ.yml
    variables:
      - userId: ${ENV(gray_user_oid)}
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]
