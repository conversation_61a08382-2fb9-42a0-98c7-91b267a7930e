- config:
    name: 获取灰度企业列表
    base_url: ${ENV(gray_manage_url)}
    variables:
      - projectKey: mx_test3
      - sql1: "update gray_version set history=1 where type=1 and history=0 and proj_key='$projectKey';"
      - sql2: "update gray_version set history=1 where type=0 and history=0 and proj_key='$projectKey';"


- test:
    name: 获取灰度企业列表-pageNum为空
    api: api/version_manage/gray_organ_list.yml
    variables:
      - pageNum: ''
    validate:
      - eq: ["content.code", 20000002]
      - contains: ["content.message", 页码不能为空]


- test:
    name: 获取灰度企业列表-pageSize为空
    api: api/version_manage/gray_organ_list.yml
    variables:
      - pageSize: ''
    validate:
      - eq: ["content.code", 20000002]
      - contains: ["content.message", 每页数量不能为空]


- test:
    name: 获取灰度企业列表-pageNum小于1
    api: api/version_manage/gray_organ_list.yml
    variables:
      - pageNum: 0
    validate:
      - eq: ["content.code", 20000002]
      - contains: ["content.message", 页码不能小于1]


- test:
    name: 获取灰度企业列表-pageSize小于1
    api: api/version_manage/gray_organ_list.yml
    variables:
      - pageSize: 0
    validate:
      - eq: ["content.code", 20000002]
      - contains: ["content.message", pageSize不能小于1]


- test:
    name: 获取灰度企业列表-pageSize大于100
    api: api/version_manage/gray_organ_list.yml
    variables:
      - pageSize: 101
    validate:
      - eq: ["content.code", 20000002]
      - contains: ["content.message", pageSize不能大于100]


- test:
    name: 新增灰度版本
    api: api/version_manage/add_version.yml
    variables:
      - version: 9.9
      - versionType: 1
      - versionNote: 自动化测试
      - upgradable: false
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]
    setup_hooks:
      - ${hook_db_data($sql1)}
      - ${hook_db_data($sql2)}


- test:
    name: 获取灰度企业列表-查询全部
    api: api/version_manage/gray_organ_list.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]
