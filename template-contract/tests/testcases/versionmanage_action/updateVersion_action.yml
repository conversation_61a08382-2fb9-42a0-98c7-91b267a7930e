- config:
    name: 编辑版本
    base_url: ${ENV(gray_manage_url)}
    variables:
      - note: 自动化测试(编辑版本)
      - projectKey: mx_test1



- test:
    name: 编辑版本-versionUuid不存在
    api: api/version_manage/update_version.yml
    variables:
      - versionUuid: 123
    validate:
      - eq: ["content.code", 20000101]
      - contains: ["content.message", 版本不存在]


- test:
    name: 编辑版本-versionNote为空
    api: api/version_manage/update_version.yml
    variables:
      - versionNote: ""
    validate:
      - eq: ["content.code", 20000002]
      - contains: ["content.message", 版本说明不能为空]


- test:
    name: 编辑版本-历史版本
    api: api/version_manage/update_version.yml
    variables:
      - sql: "select uuid from gray_version where history=1 and proj_key='$projectKey' limit 1;"
      - versionUuid: ${select_sql($sql)}
      - versionNote: $note
    validate:
      - eq: ["content.code", 20000107]
      - contains: ["content.message", 历史版本不能更新]


- test:
    name: 编辑版本-正式版本
    api: api/version_manage/update_version.yml
    variables:
      - sql: "select uuid from gray_version where type=0 and history=0 and proj_key='$projectKey';"
      - versionUuid: ${select_sql($sql)}
      - versionNote: $note
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]


- test:
    name: 编辑版本-灰度版本
    api: api/version_manage/update_version.yml
    variables:
      - sql: "select uuid from gray_version where type=1 and history=0 and proj_key='$projectKey';"
      - versionUuid: ${select_sql($sql)}
      - upgradable: true
      - versionNote: $note
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]

