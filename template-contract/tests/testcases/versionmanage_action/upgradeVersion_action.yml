- config:
    name: 升级版本
    base_url: ${ENV(gray_manage_url)}
    variables:
      - projectKey: mx_test1



- test:
    name: 升级版本-versionUuid不存在
    api: api/version_manage/upgrade_version.yml
    variables:
      - versionUuid: 123
    validate:
      - eq: ["content.code", 20000101]
      - contains: ["content.message", 版本不存在]


- test:
    name: 升级版本-历史版本
    api: api/version_manage/upgrade_version.yml
    variables:
      - sql: "select uuid from gray_version where history=1 and proj_key='$projectKey' limit 1;"
      - versionUuid: ${select_sql($sql)}
    validate:
      - eq: ["content.code", 20000102]
      - contains: ["content.message", 仅支持灰度版本强制升级]


- test:
    name: 升级版本-正式版本
    api: api/version_manage/upgrade_version.yml
    variables:
      - sql: "select uuid from gray_version where type=0 and history=0 and proj_key='$projectKey';"
      - versionUuid: ${select_sql($sql)}
    validate:
      - eq: ["content.code", 20000102]
      - contains: ["content.message", 仅支持灰度版本强制升级]


- test:
    name: 升级版本-灰度版本
    api: api/version_manage/upgrade_version.yml
    variables:
      - sql: "select uuid from gray_version where type=1 and history=0 and proj_key='$projectKey';"
      - versionUuid: ${select_sql($sql)}
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]
