- config:
    name: 获取用户灰度信息
    base_url: ${ENV(gray_manage_url)}


- test:
    name: 获取用户灰度信息-个人
    api: api/version_manage/user_gray_info.yml
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]


- test:
    name: 获取用户灰度信息-企业
    api: api/version_manage/user_gray_info.yml
    variables:
      - userId: ${ENV(gray_organ_oid)}
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]
