- config:
    name: 发布版本自增
    base_url: ${ENV(gray_manage_url)}
    variables:
      - projectKey: mx_test1



- test:
    name: 发布版本自增
    api: api/version_manage/version_increment.yml
    variables:
      - sql1: "select version from gray_version where type=0 and history=0 and proj_key='$projectKey';"
      - sql2: "select version from gray_version where type=1 and history=0 and proj_key='$projectKey';"
      - release_v: ${select_sql($sql1)}
      - gray_v: ${select_sql($sql2)}
    validate:
      - eq: ["content.code", 0]
      - contains: ["content.message", 成功]
      - str_eq: ["${versionIncrement($release_v)}", "${select_sql($sql1)}"]
      - str_eq: ["${versionIncrement($gray_v)}", "${select_sql($sql2)}"]
