config:
  name: 获取合同流程组列表


testcases:
  -
    name: 获取合同流程组列表-$CaseName
    testcase: testcases/contractmanage_action/processGroups_list_action.yml
    parameters:
      - CaseName-accountId-authorizedAccountId-processGroupName-signer<PERSON>ey<PERSON>ord-statusList-page-pageSize-code-message:
          - ["个人空间下，不带条件查询","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}","","","",1,10,0,"成功"]
          - ["个人空间下，搜索主任务主题","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}","test","","",1,10,0,"成功"]
          - ["个人空间下，搜索子任务参与人姓名","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}","","谢佳","",1,10,0,"成功"]
          - ["个人空间下，搜索子任务参与人账号","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}","","***********","",1,10,0,"成功"]
          - ["个人空间下，筛选子任务状态","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}","","","1,2",1,10,0,"成功"]
          - ["企业空间下，不带条件查询","${ENV(gray_user_oid)}","${ENV(gray_organ_oid)}","","","",1,10,0,"成功"]
