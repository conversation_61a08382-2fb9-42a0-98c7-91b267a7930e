config:
  name: 获取合同流程列表



testcases:
  -
    name: 获取合同流程列表-$CaseName
    testcase: testcases/contractmanage_action/process_list_action.yml
    parameters:
      - CaseName-accountId-subjectId-docQueryType-fuzzyMatching-processStatusList-withApproving-pageNum-pageSize-code-message:
          - ["个人空间下，我发起的列表","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}",1,"","",true,1,10,0,"成功"]
          - ["个人空间下，我收到的列表","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}",2,"","",true,1,10,0,"成功"]
          - ["个人空间下，抄送我的列表","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}",3,"","",true,1,10,0,"成功"]
          - ["个人空间下，待我操作列表","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}",5,"","",true,1,10,0,"成功"]
          - ["个人空间下，待他人操作列表","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}",6,"","",true,1,10,0,"成功"]
          - ["个人空间下，已完成列表","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}",7,"","",true,1,10,0,"成功"]
          - ["个人空间下，搜索合同主题","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}",1,"test","",true,1,10,0,"成功"]
          - ["个人空间下，搜索发起人账号","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}",1,"13506712563","",true,1,10,0,"成功"]
          - ["个人空间下，搜索参与人姓名","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}",1,"智文杰","",true,1,10,0,"成功"]
          - ["个人空间下，筛选合同状态-填写中&签署中","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}",1,"","1,2",false,1,10,0,"成功"]
          - ["企业空间下，筛选合同状态-合同审批中&合同审批拒绝&合同审批撤回","${ENV(gray_user_oid)}","${ENV(gray_organ_oid)}",1,"","3,9,10",true,1,10,0,"成功"]
