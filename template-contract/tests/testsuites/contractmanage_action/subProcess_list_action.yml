config:
  name: 获取合同流程组的子合同流程列表


testcases:
  -
    name: 获取合同流程组的子合同流程列表-$CaseName
    testcase: testcases/contractmanage_action/subProcess_list_action.yml
    parameters:
      - CaseName-processGroupId-accountId-authorizedAccountId-signerKeyWord-statusList-page-pageSize-code-message:
          - ["个人空间下，不带条件分页查询","${ENV(group_id)}","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}","","",1,10,0,"成功"]
          - ["个人空间下，搜索子任务参与人姓名","${ENV(group_id)}","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}","谢佳","",1,10,0,"成功"]
          - ["个人空间下，搜索子任务参与人账号","${ENV(group_id)}","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}","***********","",1,10,0,"成功"]
          - ["个人空间下，筛选子任务状态","${ENV(group_id)}","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}","","1,2",1,10,0,"成功"]
          - ["个人空间下，查询所有可撤回的合同","${ENV(group_id)}","${ENV(gray_user_oid)}","${ENV(gray_user_oid)}","","1,2,7,18,19","","",0,"成功"]
