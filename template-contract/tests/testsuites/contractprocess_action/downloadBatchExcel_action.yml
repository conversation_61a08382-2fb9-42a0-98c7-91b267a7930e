config:
    name: 下载批量导入表格

testcases:
-
    name: 下载批量导入表格-$CaseName
    testcase: testcases/contractprocess_action/downloadBatchExcel_action.yml
    parameters:
      - CaseName-participantLabel-flowTemplateId-excelDownTenantId-code1-message1:
          - ["模板无填写控件","乙方","${ENV(flowTemplateId1)}","${ENV(orgid2)}",0,"成功"]
          - ["模板有填写控件","乙方","${ENV(flowTemplateId2)}","${ENV(orgid2)}",0,"成功"]
          - ["非当前主体下的流程模板","乙方","${ENV(flowTemplateId2)}","${ENV(orgid3)}",312050001,"流程模板不属于该账号"]
          - ["flowTemplateId为空-直接发起","乙方","","${ENV(orgid2)}",0,"成功"]
          - ["flowTemplateId不为空-模板发起","乙方","${ENV(flowTemplateId3)}","${ENV(ouid)}",0,"成功"]





