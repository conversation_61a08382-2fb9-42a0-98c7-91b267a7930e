config:
    name: 解析批量导入表格

testcases:
-
    name: 解析批量导入表格-$CaseName
    testcase: testcases/contractprocess_action/parseBatchExcel_action.yml
    parameters:
      - CaseName-fileKey-participantLabel-flowTemplateId-parseBatchTenantId-code1-message1:
          - ["正常解析批量导入表格-无填写控件","${ENV(batchfileKey1)}","乙方","${ENV(flowTemplateId1)}","${ENV(orgid2)}",0,"成功"]
          - ["解析批量导入表格-表格数据异常","${ENV(fileKey1)}","乙方","${ENV(flowTemplateId1)}","${ENV(orgid2)}",312020011,"解析批量导入表格失败:解析异常"] # 2020年3月2号原来的message信息：解析批量导入表格失败
          - ["解析非当前主体下的流程的批量导入表格","${ENV(batchfileKey1)}","乙方","${ENV(flowTemplateId1)}","${ENV(orgid3)}",312050001,"流程模板不属于该账号"]
          - ["flowTemplateId为空-直接发起解析表格数据","${ENV(batchfileKey3)}","乙方","","${ENV(orgid2)}",0,"成功"]





