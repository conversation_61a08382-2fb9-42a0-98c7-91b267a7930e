config:
    name: 流程发起详情信息

testcases:
-
    name: 流程发起详情信息-$CaseName
    testcase: testcases/contractprocess_action/processDetail_action.yml
    parameters:
      - CaseName-flowTemplateId-detailTenantId-code1-message1:
          - ["正常获取流程发起详情","${ENV(flowTemplateId1)}","${ENV(orgid2)}",0,"成功"]
          - ["获取非当前主体下的流程模板详情","${ENV(flowTemplateId1)}","${ENV(orgid3)}",312050001,"流程模板不属于该账号"]
          - ["获取个人主体下流程模板详情","${ENV(flowTemplateId3)}","${ENV(ouid)}",0,"成功"]

