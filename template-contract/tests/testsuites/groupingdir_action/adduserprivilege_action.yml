config:
    name: 添加目录用户及授权

testcases:
-
    name: 添加目录用户及授权-$CaseName
    testcase: testcases/groupingdir_action/adduserprivilege_action.yml
    parameters:
      - CaseName-accountId1-name-parentMenuId-createTenantId-code1-message1-ouidadduser-username-useroid-roleId-mobileNo-addTenantId-code2-message2-accountId2-deleteTenantId-code3-message3:
          - ["正常添加用户并授权-可编辑权限","${ENV(ouid)}","add_test1","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","武玉华","${ENV(ouid2)}","4d80868d4a1e4881b8237e702738a5b2","***********","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]
          - ["正常添加用户并授权-可下载权限","${ENV(ouid)}","add_test2","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","武玉华","${ENV(ouid2)}","583aff2b2eae48ebb19da363b7160ea5","***********","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]
          - ["正常添加用户并授权-可查看权限","${ENV(ouid)}","add_test3","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","武玉华","${ENV(ouid2)}","2543756aea364646a64e6a4de063ab6d","***********","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]
          - ["添加用户oid非企业添加成员","${ENV(ouid)}","add_test4","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","马化腾","324148e0e45b46bc9e891fe9305f2238","2543756aea364646a64e6a4de063ab6d","***********","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]
          - ["非企业管理员添加用户授权","${ENV(ouid)}","add_test5","","${ENV(orgid)}",0,"成功","${ENV(ouid2)}","刘德华","324148e0e45b46bc9e891fe9305f2238","2543756aea364646a64e6a4de063ab6d","***********","${ENV(orgid)}",30903002,"无操作权限","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]



