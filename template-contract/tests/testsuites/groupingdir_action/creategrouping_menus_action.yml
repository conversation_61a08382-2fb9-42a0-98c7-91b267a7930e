config:
    name: 创建归档目录

testcases:
-
    name: 创建归档目录-$CaseName
    testcase: testcases/groupingdir_action/creategrouping_menus_action.yml
    parameters:
      - CaseName-accountId1-name-parentMenuId-createTenantId-code1-message1-accountId2-deleteTenantId-code2-message2:
          - ["正常创建个人归档一级目录","${ENV(ouid)}","${getTimeStamp()}_one1","","${ENV(ouid)}",0,"成功","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]
          - ["正常创建企业归档一级目录","${ENV(ouid)}","${getTimeStamp()}_two2","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]
          - ["name为空","${ENV(ouid)}","","","${ENV(orgid)}",********,"参数错误: name不能为空","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]
          - ["非管理员账户创建目录","${ENV(ouid2)}","${getTimeStamp()}_other","","${ENV(orgid)}",********,"无操作权限","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]