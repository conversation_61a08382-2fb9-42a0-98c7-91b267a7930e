config:
    name: 创建归档四级目录

testcases:
-
    name: 创建归档目录-$CaseName
    testcase: testcases/groupingdir_action/creategrouping_menus_four_action.yml
    parameters:
      - CaseName-accountId1-name-parentMenuId-createTenantId-code1-message1-oidtwo-nametwo-createTenantIdtwo-code2-message2-oid3-name3-createTenantId3-code3-message3-oid4-name4-createTenantId4-code4-message4-accountId2-deleteTenantId-code5-message5:
#          - ["个人下创建归档四级目录","${ENV(ouid)}","one-${getTimeStamp()}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}","2-${getTimeStamp()}","${ENV(ouid)}",0,"成功","${ENV(ouid)}","3-${getTimeStamp()}","${ENV(ouid)}",0,"成功","${ENV(ouid)}","groupingtest4","${ENV(ouid)}",********,"分类最多仅支持三级结构","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]
          - ["企业下创建归档四级目录","${ENV(ouid)}","q1-${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","q2-${getTimeStamp()}","${ENV(orgid)}",0,"成功","${ENV(ouid)}","q3-${getTimeStamp()}","${ENV(orgid)}",0,"成功","${ENV(ouid)}","cgroupingtest4","${ENV(orgid)}",********,"分类最多仅支持三级结构","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]

