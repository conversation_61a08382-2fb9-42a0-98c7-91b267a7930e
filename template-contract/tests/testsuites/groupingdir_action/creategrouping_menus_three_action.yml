config:
    name: 创建归档三级目录

testcases:
-
    name: 创建归档目录-$CaseName
    testcase: testcases/groupingdir_action/creategrouping_menus_three_action.yml
    parameters:
      - CaseName-accountId1-name-parentMenuId-createTenantId-code1-message1-oidtwo-nametwo-createTenantIdtwo-code2-message2-oid3-name3-createTenantId3-code3-message3-accountId2-deleteTenantId-code4-message4:
          - ["正常创建个人下归档三级目录","${ENV(ouid)}","per-1_${getTimeStamp()}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}","oii1-${getTimeStamp()}","${ENV(ouid)}",0,"成功","${ENV(ouid)}","3ED-1${getTimeStamp()}","${ENV(ouid)}",0,"成功","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]
          - ["正常创建企业下归档三级目录","${ENV(ouid)}","qiye1-${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","qit-2${getTimeStamp()}","${ENV(orgid)}",0,"成功","${ENV(ouid)}","qiye-34${getTimeStamp()}","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]
          - ["创建三级同名目录","${ENV(ouid)}","samename1","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","groupingtest2","${ENV(orgid)}",0,"成功","${ENV(ouid)}","groupingtest2","${ENV(orgid)}",********,"分类名称已被占用,请重新输入","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]
