config:
    name: 创建归档二级目录

testcases:
-
    name: 创建归档目录-$CaseName
    testcase: testcases/groupingdir_action/creategrouping_menus_two_action.yml
    parameters:
      - CaseName-accountId1-name-parentMenuId-createTenantId-code1-message1-oidtwo-nametwo-createTenantIdtwo-code2-message2-accountId2-deleteTenantId-code3-message3:
          - ["正常创建个人下归档二级目录","${ENV(ouid)}","qw_${getTimeStamp()}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}","2-2-${getTimeStamp()}","${ENV(ouid)}",0,"成功","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]
          - ["正常创建企业下归档二级目录","${ENV(ouid)}","qw2r_${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","q-2-2${getTimeStamp()}","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]
          - ["创建同名二级目录","${ENV(ouid)}","QINGLIAN1","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","QINGLIAN1","${ENV(orgid)}",********,"分类名称已被占用,请重新输入","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]