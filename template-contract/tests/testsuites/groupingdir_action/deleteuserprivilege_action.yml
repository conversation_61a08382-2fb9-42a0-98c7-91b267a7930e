config:
    name: 删除目录用户

testcases:
-
    name: 删除目录用户-$CaseName
    testcase: testcases/groupingdir_action/deleteuserprivilege_action.yml
    parameters:
      - CaseName-accountId1-name-parentMenuId-createTenantId-code1-message1-ouidadduser-username-useroid-roleId-mobileNo-addTenantId-code2-message2-deleteuseroid-listoid-deleteuserTenantId-code3-message3-accountId2-deleteTenantId-code4-message4:
          - ["正常删除目录用户-可编辑权限","${ENV(ouid)}","delete-1-${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","武玉华","${ENV(ouid2)}","4d80868d4a1e4881b8237e702738a5b2","***********","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(ouid2)}","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]





