config:
    name: 获取当前用户的目录列表

testcases:
-
    name: 获取当前用户的目录列表-$CaseName
    testcase: testcases/groupingdir_action/listgrouping_menus_action.yml
    parameters:
      - CaseName-accountId-listTenantId-code2-message2:
          - ["获取当前用户个人的目录列表","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]
          - ["获取当前用户企业的目录列表","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]
          - ["获取非企业管理员用户的目录列表","${ENV(ouid2)}","${ENV(orgid)}",0,"成功"]
          - ["accountId不存在","215125uhugUguiiu","${ENV(ouid)}",********,"组织架构不存在"]

