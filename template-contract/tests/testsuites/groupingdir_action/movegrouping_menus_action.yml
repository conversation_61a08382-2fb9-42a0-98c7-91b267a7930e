config:
    name: 移动归档目录

testcases:
-
    name: 移动归档目录-$CaseName
    testcase: testcases/groupingdir_action/movegrouping_menus_action.yml
    parameters:
      - CaseName-accountId1-name-parentMenuId-createTenantId-code1-message1-oid4-name4-parentMenuId-createTenantId4-code2-message2-accountId4-sourceMenuId-targetOrder-moveTenantId-code3-message3-accountId2-deleteTenantId-code4-message4-deleteoid2-deleteTenantId2-code5-message5:
          - ["正常移动个人下归档目录","${ENV(ouid)}","tyu-${getTimeStamp()}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}","ukj-${getTimeStamp()}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}","","","${ENV(ouid)}",0,"成功","${ENV(ouid)}","${ENV(ouid)}",0,"成功","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]
          - ["正常移动企业下归档目录","${ENV(ouid)}","iuy-${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","ikl-0-${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]




