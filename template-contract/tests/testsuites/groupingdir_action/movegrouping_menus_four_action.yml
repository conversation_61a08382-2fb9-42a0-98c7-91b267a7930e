config:
    name: 移动归档目录到三级目录下

testcases:
-
    name: 移动归档目录到三级目录下-$CaseName
    testcase: testcases/groupingdir_action/movegrouping_menus_four_action.yml
    parameters:
      - CaseName-accountId1-name-parentMenuId-createTenantId-code1-message1-oidtwo-nametwo-createTenantIdtwo-code2-message2-oid3-name3-createTenantId3-code3-message3-oid4-name4-parentMenuId-createTenantId4-code4-message4-moveoid4-sourceMenuId-targetOrder-moveTenantId-code5-message5-accountId2-deleteTenantId-code6-message6-deleteoid2-deleteTenantId2-code7-message7:
          - ["个人下移动归档目录到三级目录下","${ENV(ouid)}","mo-1-${getTimeStamp()}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}","mo-2-${getTimeStamp()}","${ENV(ouid)}",0,"成功","${ENV(ouid)}","mo-3-${getTimeStamp()}","${ENV(ouid)}",0,"成功","${ENV(ouid)}","mod-${getTimeStamp()}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}","","","${ENV(ouid)}",********,"分类最多仅支持三级结构","${ENV(ouid)}","${ENV(ouid)}",0,"成功","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]
          - ["企业下移动归档目录到三级目录下","${ENV(ouid)}","moq-1-${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","moq-2-${getTimeStamp()}","${ENV(orgid)}",0,"成功","${ENV(ouid)}","moq-3-${getTimeStamp()}","${ENV(orgid)}",0,"成功","${ENV(ouid)}","moq-4${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","","","${ENV(orgid)}",********,"分类最多仅支持三级结构","${ENV(ouid)}","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]


