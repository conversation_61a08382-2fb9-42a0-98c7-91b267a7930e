config:
    name: 更新归档目录

testcases:
-
    name: 更新归档目录-$CaseName
    testcase: testcases/groupingdir_action/updategrouping_menus_action.yml
    parameters:
      - CaseName-accountId1-name-parentMenuId-createTenantId-code1-message1-name3-accountId3-updateTenantId-code2-message2-accountId2-deleteTenantId-code3-message3:
          - ["正常更新归档目录","${ENV(ouid)}","update_${getTimeStamp()}","","${ENV(ouid)}",0,"成功","update_2${getTimeStamp()}","${ENV(ouid)}","${ENV(ouid)}",0,"成功","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]
          - ["更新目录为原目录","${ENV(ouid)}","update_3${getTimeStamp()}","","${ENV(ouid)}",0,"成功","update_4${getTimeStamp()}","${ENV(ouid)}","${ENV(ouid)}",0,"成功","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]

