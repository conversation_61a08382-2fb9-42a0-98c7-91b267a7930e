config:
    name: 合同归档

testcases:
-
    name: 合同归档-$CaseName
    testcase: testcases/groupingfile_action/grouping_file_action.yml
    parameters:
      - CaseName-accountId1-name-parentMenuId-createTenantId-code1-message1-groupoid-processIdList-validTime-groupTenantId-code2-message2-processId-removeoid-targetMenuId-removeTenantId-code3-message3-accountId2-deleteTenantId-code4-message4:
          - ["正常归档->个人下归档单个文件","${ENV(ouid)}","normalG-${getTimeStamp()}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}",['${ENV(processId1)}'],"","${ENV(ouid)}",0,"成功","${ENV(processId1)}","${ENV(ouid)}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]
          - ["正常归档->企业下归档单个文件","${ENV(ouid)}","normalQ-${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}",['${ENV(processId_org1)}'],"","${ENV(orgid)}",0,"成功","${ENV(processId_org1)}","${ENV(ouid)}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]
#          - ["归档文件不存在","${ENV(ouid)}","noexist-${getTimeStamp()}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}",['13124asd'],"","${ENV(ouid)}",********,"无操作权限","${ENV(processId1)}","${ENV(ouid)}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]
          - ["归档操作人非企业管理员","${ENV(ouid)}","notor-${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid2)}",['${ENV(processId_org1)}'],"","${ENV(orgid)}",********,"无操作权限","${ENV(processId_org1)}","${ENV(ouid)}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]
          - ["归档分类为空","${ENV(ouid2)}","null-${getTimeStamp()}","","${ENV(orgid)}",********,"无操作权限","${ENV(ouid2)}",['${ENV(processId_org1)}'],"","${ENV(orgid)}",30903004,"归档分类不能为空","${ENV(processId_org1)}","${ENV(ouid2)}","","${ENV(orgid)}",31202000,"参数错误: targetMenuId不能为空","${ENV(ouid2)}","${ENV(orgid)}",********,"无操作权限"]



