config:
    name: 移动合同到其他目录

testcases:
-
    name: 移动合同到其他目录-$CaseName
    testcase: testcases/groupingfile_action/movegrouping_file_action.yml
    parameters:
      - CaseName-accountId1-name-parentMenuId-createTenantId-code1-message1-oidtwo-nametwo-createTenantIdtwo-code2-message2-groupoid-processIdList-validTime-groupTenantId-code3-message3-processId2-moveoid-moveTenantId-code4-message4-processId-removeoid-targetMenuId-removeTenantId-code5-message5-accountId2-deleteTenantId-code6-message6:
          - ["个人下移动合同到其他目录","${ENV(ouid)}","move-${getTimeStamp()}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}","move2-${getTimeStamp()}","${ENV(ouid)}",0,"成功","${ENV(ouid)}",['${ENV(processId1)}'],"","${ENV(ouid)}",0,"成功","${ENV(processId1)}","${ENV(ouid)}","${ENV(ouid)}",0,"成功","${ENV(processId1)}","${ENV(ouid)}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]
          - ["企业下移动合同到其他目录","${ENV(ouid)}","moving-${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","moving2-${getTimeStamp()}","${ENV(orgid)}",0,"成功","${ENV(ouid)}",['${ENV(processId_org1)}'],"","${ENV(orgid)}",0,"成功","${ENV(processId_org1)}","${ENV(ouid)}","${ENV(orgid)}",0,"成功","${ENV(processId_org1)}","${ENV(ouid)}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]


