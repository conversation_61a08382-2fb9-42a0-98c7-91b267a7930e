config:
    name: 移出分类

testcases:
-
    name: 移出分类-$CaseName
    testcase: testcases/groupingfile_action/removegrouping_file_action.yml
    parameters:
      - CaseName-accountId1-name-parentMenuId-createTenantId-code1-message1-groupoid-processIdList-validTime-groupTenantId-code2-message2-processId-removeoid-targetMenuId-removeTenantId-code3-message3-accountId2-deleteTenantId-code4-message4:
          - ["个人下移出分类","${ENV(ouid)}","remove-${getTimeStamp()}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}",['${ENV(processId1)}'],"","${ENV(ouid)}",0,"成功","${ENV(processId1)}","${ENV(ouid)}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]
          - ["企业下移除分类","${ENV(ouid)}","removing-${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}",['${ENV(processId_org1)}'],"","${ENV(orgid)}",0,"成功","${ENV(processId_org1)}","${ENV(ouid)}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]
          - ["非企业管理员移除分类","${ENV(ouid)}","uuu-${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}",['${ENV(processId_org1)}'],"","${ENV(orgid)}",0,"成功","${ENV(processId_org1)}","${ENV(ouid2)}","","${ENV(orgid)}",********,"无操作权限","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]


