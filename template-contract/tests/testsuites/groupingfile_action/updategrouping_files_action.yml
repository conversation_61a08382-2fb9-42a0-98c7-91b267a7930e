config:
    name: 更新合同信息

testcases:
-
    name: 更新合同信息-$CaseName
    testcase: testcases/groupingfile_action/updategrouping_file_action.yml
    parameters:
      - CaseName-accountId1-name-parentMenuId-createTenantId-code1-message1-groupoid-processIdList-validTime-groupTenantId-code2-message2-processId_UP-upoid-validTime2-updateTenantId-code3-message3-processId-removeoid-targetMenuId-removeTenantId-code4-message4-accountId2-deleteTenantId-code5-message5:
          - ["正常更新个人下合同信息","${ENV(ouid)}","update-${getTimeStamp()}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}",['${ENV(processId1)}'],"","${ENV(ouid)}",0,"成功","${ENV(processId1)}","${ENV(ouid)}","${groupingNum()}","${ENV(ouid)}",0,"成功","${ENV(processId1)}","${ENV(ouid)}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]
          - ["正常更新企业下合同信息","${ENV(ouid)}","update2-${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}",['${ENV(processId_org1)}'],"","${ENV(orgid)}",0,"成功","${ENV(processId_org1)}","${ENV(ouid)}","${groupingNum()}","${ENV(orgid)}",0,"成功","${ENV(processId_org1)}","${ENV(ouid)}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]
          - ["个人下编辑非当前操作人主体的合同有效期","${ENV(ouid)}","update3-${getTimeStamp()}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}",['${ENV(processId2)}'],"","${ENV(ouid)}",0,"成功","${ENV(processId2)}","${ENV(ouid)}","${groupingNum()}","${ENV(ouid)}",30903002,"无操作权限","${ENV(processId2)}","${ENV(ouid)}","","${ENV(ouid)}",0,"成功","${ENV(ouid)}","${ENV(ouid)}",0,"成功"]
          - ["编辑填写中流程的合同有效期","${ENV(ouid)}","update4-${getTimeStamp()}","","${ENV(orgid2)}",0,"成功","${ENV(ouid)}",['${ENV(processId_fillin)}'],"","${ENV(orgid2)}",0,"成功","${ENV(processId_fillin)}","${ENV(ouid)}","${groupingNum()}","${ENV(orgid2)}",0,"成功","${ENV(processId_fillin)}","${ENV(ouid)}","","${ENV(orgid2)}",0,"成功","${ENV(ouid)}","${ENV(orgid2)}",0,"成功"]

