config:
    name: 已归档列表查询

testcases:
-
    name: 已归档列表查询-$CaseName
    testcase: testcases/groupingquery/groupinglistquery_action.yml
    parameters:
      - CaseName-querylistoid-menuId-title-initiator-createFrom-createEnd-querylistTenantId-code1-message1:
          - ["查询已归档列表title->数据为空","${ENV(ouid)}","","XXX123","","","","${ENV(ouid)}",0,"成功"]
          - ["查询已归档列表initiator","${ENV(ouid)}","","","宁","","","${ENV(ouid)}",0,"成功",]
          - ["查询已归档列表->发起时间查询","${ENV(ouid)}","","","",1561910400000,1570377600000,"${ENV(ouid)}",0,"成功",]


