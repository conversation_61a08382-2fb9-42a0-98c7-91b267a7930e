config:
    name: 待归档列表查询

testcases:
-
    name: 待归档列表查询-$CaseName
    testcase: testcases/groupingquery/ungroupinglistquery_action.yml
    parameters:
      - CaseName-unquerylistoid-menuId-title-initiator-createFrom-createEnd-unquerylistTenantId-code1-message1:
          - ["待归档列表查询title->数据为空","${ENV(ouid)}","","XXX123","","","","${ENV(ouid)}",0,"成功"]
          - ["待归档列表查询initiator","${ENV(ouid)}","","","宁","","","${ENV(ouid)}",0,"成功",]
          - ["待归档列表查询->发起时间查询","${ENV(ouid)}","","","",1561910400000,1570377600000,"${ENV(ouid)}",0,"成功",]


