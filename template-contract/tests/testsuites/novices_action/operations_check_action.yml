config:
    name: 判断用户指定新手操作是否已完成

testcases:
-
    name: 判断用户指定新手操作是否已完成
    testcase: testcases/novices_action/operations_check_action.yml
    parameters:
      - CaseName-accountId-code1-message1-oprationName:
          - ["忽略任务","${ENV(novices_oid)}",0,"成功","ignore_novice_task"]
          - ["发起新人引导","${ENV(novices_oid)}",0,"成功","initiate_novice_guide"]
          - ["指定位置新人引导","${ENV(novices_oid)}",0,"成功","assigned_pos_novice_guide"]
          - ["合同管理新人引导","${ENV(novices_oid)}",0,"成功","process_list_novice_guide"]