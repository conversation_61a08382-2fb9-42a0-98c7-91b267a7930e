config:
    name: 完成用户指定新手操作

testcases:
-
    name: 完成用户指定新手操作
    testcase: testcases/novices_action/operations_complete_action.yml
    parameters:
      - CaseName-accountId-code1-message1-operateName:
          - ["忽略任务","${ENV(novices_oid)}",0,"成功","ignore_novice_task"]
          - ["发起新人引导","${ENV(novices_oid)}",0,"成功","initiate_novice_guide"]
          - ["指定位置新人引导","${ENV(novices_oid)}",0,"成功","assigned_pos_novice_guide"]
          - ["合同管理新人引导","${ENV(novices_oid)}",0,"成功","process_list_novice_guide"]