config:
  name: 获取流程模板控件信息

testcases:
-
    name: 获取流程模板控件信息-$CaseName
    testcase: testcases/openflowtemplate_action/getFlowtemplate_structComponents_Info_action.yml
    parameters:
      - CaseName-flowTemplateId-operatorId-tenantId-participantId-code1-message1:
          - ["(个人)获取流程模板控件详细信息","${ENV(flowtemplateId)}","","${ENV(accountId)}","",0,"成功"]
          - ["(个人)获取流程模板控件详细信息,flowtemplateId已经删除","${ENV(flowtemplateId_delete)}","","${ENV(accountId)}","",*********,"流程模板不存在"]
          - ["(个人)获取流程模板控件详细信息,租户id为空","${ENV(flowtemplateId)}","","","",*********,"账号不存在或已注销"]
          - ["(个人)获取流程模板控件详细信息,租户id不存在","${ENV(flowtemplateId)}","","*********","",*********,"账号不存在或已注销"]
          - ["(个人)获取流程模板控件详细信息,租户id已经删除","${ENV(flowtemplateId)}","","${ENV(accountIddelete)}","",*********,"账号不存在或已注销"]
          - ["(个人)获取流程模板控件详细信息,参与方id填写获取信息","${ENV(flowtemplateId)}","${ENV(accountId)}","${ENV(accountId)}","${ENV(participantId)}",0,"成功"]
          - ["(个人)获取流程模板控件详细信息,参与方不存在（返回全部信息）","${ENV(flowtemplateId)}","${ENV(accountId)}","${ENV(accountId)}","68666rewrw",0,"成功"]
          - ["(企业)获取流程模板控件详细信息，流程模板账号不匹配","${ENV(flowtemplateId)}","${ENV(accountId)}","${ENV(org_accountId)}","",*********,"流程模板不属于该账号"]
          - ["(企业)获取流程模板控件详细信息,正常获取","${ENV(flowtemplateId_org)}","${ENV(accountId)}","${ENV(org_accountId)}","",0,"成功"]
#          - ["(企业)获取流程模板控件详细信息,流程模板为空","","${ENV(accountId)}","${ENV(org_accountId)}","",*********,"流程模板不存在"]
          - ["(企业)获取流程模板控件详细信息,流程模板不存在","**********","${ENV(accountId)}","${ENV(org_accountId)}","",*********,"流程模板不存在"]
#          - ["(企业)获取流程模板详细信息,操作人id为空","${ENV(flowtemplateId_org)}","","${ENV(org_accountId)}",********,"fail"]{'code': ********, 'message': '缺少参数: accountId must not be empty', 'data': None}
          - ["(企业)获取流程模板详细信息,操作人id不存在","${ENV(flowtemplateId_org)}","***********","${ENV(org_accountId)}","",*********,"账号不存在或已注销"]
          - ["(企业)获取流程模板控件详细信息,操作人id已注销","${ENV(flowtemplateId_org)}","${ENV(accountIddelete)}","${ENV(org_accountId)}","",*********,"企业成员不存在"]
          - ["(企业)获取流程模板控件详细信息,租户人id为空","${ENV(flowtemplateId_org)}","${ENV(accountId)}","","",*********,"账号不存在或已注销"]
          - ["(企业)获取流程模板控件详细信息,租户人id不存在","${ENV(flowtemplateId_org)}","${ENV(accountId)}","6363673","",*********,"账号不存在或已注销"]
          - ["(企业)获取流程模板控件详细信息,租户人id已经删除","${ENV(flowtemplateId_org)}","${ENV(accountId)}","${ENV(orgIddelete)}","",*********,"账号不存在或已注销"]
          - ["(企业)获取流程模板控件详细信息,参与方id填写","${ENV(flowtemplateId_org)}","${ENV(accountId)}","${ENV(org_accountId)}","${ENV(participantId_org)}",0,"成功"]
          - ["(企业)获取流程模板控件详细信息,参与方不存在（返回控件信息）","${ENV(flowtemplateId_org)}","${ENV(accountId)}","${ENV(org_accountId)}","68666rewrw",0,"成功"]




    







