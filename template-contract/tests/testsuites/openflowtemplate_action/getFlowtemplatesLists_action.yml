config:
  name: 获取流程模板列表

testcases:
-
    name: 获取流程模板列表-$CaseName
    testcase: testcases/openflowtemplate_action/getFlowtemplateLists_action.yml
    parameters:
      - CaseName-tenantId-pageNo-pageSize-keyword-code1-message1:
          - ["(个人)获取流程模板列表","${ENV(accountId)}",1,10,"",0,"成功"]
          - ["(个人)获取流程模板列表,tenantId为空","",1,10,"",*********,"账号不存在或已注销"]
          - ["(个人)获取流程模板列表,PageNo为-1","${ENV(accountId)}",-1,10,"",0,"成功"]
          - ["(个人)获取流程模板列表,PageNo为0","${ENV(accountId)}",0,10,"",0,"成功"]
          - ["(个人)获取流程模板列表,PageSize为-1","${ENV(accountId)}",1,-1,"",0,"成功"]
          - ["(个人)获取流程模板列表,PageSize为50","${ENV(accountId)}",0,50,"",0,"成功"]
          - ["(个人)获取流程模板列表,PageSize为100（大于50,默认50）","${ENV(accountId)}",0,100,"",0,"成功"]
          - ["(个人)获取流程模板列表,keyWord为模糊查询","${ENV(accountId)}",0,10,"开放能力",0,"成功"]
          - ["(个人)获取流程模板列表,keyWord字段长度为>64（数据库字段，流程模板名称长度64）","${ENV(accountId)}",0,10,"${generate_random_str(65)}",0,"成功"]
          - ["(企业)获取流程模板列表","${ENV(org_accountId)}",1,10,"",0,"成功"]

    







