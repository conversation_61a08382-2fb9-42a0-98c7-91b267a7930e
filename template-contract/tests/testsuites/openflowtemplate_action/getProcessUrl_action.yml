config:
  name: 获取流程模板操作地址

testcases:
-
    name: 获取流程模板创建/设置地址-$CaseName
    testcase: testcases/openflowtemplate_action/getProcessUrl_action.yml
    parameters:
      - CaseName-processId-accountId-subjectId-platform-redirectUrl-code-message:
#          填写的短链没有subjectId
          - ["(个人)获取流程模板操作地址（填写）","${ENV(processId_XYopen)}","${ENV(accountId_sunyang)}","${ENV(accountId_sunyang)}",1,"http://172.20.62.157/simpleTools/notice/",0,"成功"]
#          - ["(个人)获取流程模板操作地址,processId为空","","${ENV(accountId_sunyang)}","${ENV(accountId_sunyang)}",1,"",0,"成功"]
          - ["(个人)获取流程模板操作地址（填写）,processId和人不对应","${ENV(processId_XYopen)}","${ENV(ouid)}","${ENV(ouid)}",1,"http://172.20.62.157/simpleTools/notice/",********,"操作人无权限"]
          - ["(个人)获取流程模板操作地址（填写）,processId不存在","*********","${ENV(accountId)}","${ENV(accountId)}",1,"http://172.20.62.157/simpleTools/notice/",********,"合同流程不存在"]
          - ["(个人)获取流程模板操作地址（填写）,accountId为空","${ENV(processId_XYopen)}","","${ENV(accountId)}",1,"http://172.20.62.157/simpleTools/notice/",*********,"账号不存在或已注销"]
          - ["(个人)获取流程模板操作地址（填写）,accountId不存在","${ENV(processId_XYopen)}","**********","${ENV(accountId)}",1,"http://172.20.62.157/simpleTools/notice/",*********,"账号不存在或已注销"]
          - ["(个人)获取流程模板操作地址（填写）,subjectId为空","${ENV(processId_XYopen)}","${ENV(accountId)}","",1,"http://172.20.62.157/simpleTools/notice/",0,"成功"]
          - ["(个人)获取流程模板操作地址（填写）,subjectId不存在","${ENV(processId_XYopen)}","${ENV(accountId)}","*********",1,"http://172.20.62.157/simpleTools/notice/",0,"成功"]
          - ["(个人)获取流程模板操作地址（填写）,platfrom为2","${ENV(processId_XYopen)}","${ENV(accountId_sunyang)}","${ENV(accountId_sunyang)}",2,"http://172.20.62.157/simpleTools/notice/",0,"成功"]
          - ["(个人)获取流程模板操作地址（填写）,回调地址esign.cn","${ENV(processId_XYopen)}","${ENV(accountId_sunyang)}","${ENV(accountId_sunyang)}",1,"esign.cn",0,"成功"]
          - ["(个人)获取流程模板操作地址（填写）","${ENV(processId_XYopen)}","${ENV(accountId_sunyang)}","${ENV(accountId_sunyang)}",1,"http://172.20.62.157/simpleTools/notice/",0,"成功"]
        #          - ["(个人)获取流程模板操作地址,processId为空","","${ENV(accountId_sunyang)}","${ENV(accountId_sunyang)}",1,"http://172.20.62.157/simpleTools/notice/",0,"成功"]
          - ["(企业)获取流程模板操作地址（填写），processId和人不对应","${ENV(processId_XYopen_org)}","${ENV(ouid)}","${ENV(ouid)}",1,"http://172.20.62.157/simpleTools/notice/",********,"操作人无权限"]
          - ["(企业)获取流程模板操作地址（填写），processId不存在","*********","${ENV(accountId)}","${ENV(accountId)}",1,"http://172.20.62.157/simpleTools/notice/",********,"合同流程不存在"]
          - ["(企业)获取流程模板操作地址（填写），accountId为空","${ENV(processId_XYopen)}","","${ENV(accountId)}",1,"http://172.20.62.157/simpleTools/notice/",*********,"账号不存在或已注销"]
          - ["(企业)获取流程模板操作地址（填写），accountId不存在","${ENV(processId_XYopen)}","**********","${ENV(accountId)}",1,"",*********,"账号不存在或已注销"]
          - ["(企业)获取流程模板操作地址（填写），subjectId为空","${ENV(processId_XYopen)}","${ENV(accountId)}","",1,"http://172.20.62.157/simpleTools/notice/",0,"成功"]
          - ["(企业)获取流程模板操作地址（填写），subjectId不存在","${ENV(processId_XYopen)}","${ENV(accountId)}","*********",1,"",0,"成功"]
          - ["(企业)获取流程模板操作地址（填写），platfrom为2","${ENV(processId_XYopen_org)}","${ENV(accountId_sunyang)}","${ENV(org_accountId)}",2,"http://172.20.62.157/simpleTools/notice/",0,"成功"]
          - ["(企业)获取流程模板操作地址（填写），回调地址esign.cn","${ENV(processId_XYopen_org)}","${ENV(accountId_sunyang)}","${ENV(org_accountId)}",1,"esign.cn",0,"成功"]












