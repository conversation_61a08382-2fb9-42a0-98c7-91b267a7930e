config:
  name: 获取流程模板创建/设置地址

testcases:
-
    name: 获取流程模板创建/设置地址-$CaseName
    testcase: testcases/openflowtemplate_action/getSettingUrl_action.yml
    parameters:
      - CaseName-operatorId-tenantId-flowTemplateId-code1-message1:
          - ["(个人)获取流程模板创建（flowtemplateId传空）","${ENV(accountId)}","${ENV(accountId)}","",0,"成功"]
          - ["(个人)操作人id为空","","${ENV(accountId)}","",0,"成功"]
          - ["(个人)操作人id不存在","${generate_random_str(32)}","${ENV(accountId)}","",0,"成功"]
          - ["(个人)操作人id字段长度超长出数据库字段长度","${generate_random_str(51)}","${ENV(accountId)}","",0,"成功"]
          - ["(个人)租户人id为空","${ENV(accountId)}","","",*********,"账号不存在或已注销"]
          - ["(个人)租户人id不存在","${ENV(accountId)}","${generate_random_str(51)}","",*********,"账号不存在或已注销"]
          - ["(个人)租户人id已注销","${ENV(accountIddelete)}","${ENV(accountIddelete)}","",*********,"账号不存在或已注销"]
          - ["(个人)flowtemplateId,已创建","${ENV(accountId)}","${ENV(accountId)}","${ENV(flowtemplateId)}",0,"成功"]
          - ["(个人)flowtemplateId不存在","${ENV(accountId)}","${ENV(accountId)}","4234762436724274723", *********,"流程模板不存在"]
          - ["(个人)flowtemplateId已经删除","${ENV(accountId)}","${ENV(accountId)}","${ENV(flowtemplateId_delete)}",*********,"流程模板不存在"]
          - ["(企业)获取流程模板创建（flowtemplateId传空）","${ENV(accountId)}","${ENV(org_accountId)}","",0,"成功"]
          - ["(企业)操作人id为空","","${ENV(org_accountId)}","",********,"缺少参数: accountId must not be empty"]
          - ["(企业)操作人id不存在","${generate_random_str(32)}","${ENV(org_accountId)}","",*********,"账号不存在或已注销"]
          - ["(企业)操作人id字段长度超长出数据库字段长度","${generate_random_str(51)}","${ENV(org_accountId)}","",*********,"账号不存在或已注销"]
          - ["(企业)租户人id为空","${ENV(accountId)}","","",*********,"账号不存在或已注销"]
          - ["(企业)租户人id不存在","${ENV(accountId)}","${generate_random_str(51)}","",*********,"账号不存在或已注销"]
          - ["(企业)租户人id已注销","${ENV(accountId)}","${ENV(orgIddelete)}","",*********,"账号不存在或已注销"]
#          - ["(企业)flowtemplateId,已创建","${ENV(accountId)}","${ENV(accountId)}","${ENV(flowtemplateId)}",0,"成功"]
#          - ["(企业)flowtemplateId不存在","${ENV(accountId)}","${ENV(accountId)}","4234762436724274723", *********,"流程模板不存在"]
#          - ["(企业)flowtemplateId已经删除","${ENV(accountId)}","${ENV(accountId)}","${ENV(flowtemplateId_delete)}",*********,"流程模板不存在"]







