config:
  name: 个人模板发起
#  variables:
#    participantId_sunyang_02: ${ENV(participantId_sunyang_02)}
#    participantId_sunyang_03: ${ENV(participantId_sunyang_03)}
#    participantId_zyy_03: ${ENV(participantId_zyy_03)}
#    participantId_sunyang_04: ${ENV(participantId_sunyang_04)}
#    participantId_zyy_04: ${ENV(participantId_zyy_04)}
#    participantId_zyy_05: ${ENV(participantId_zyy_05)}
#    participantId_sunyang_05: ${ENV(participantId_sunyang_05)}
#    participantId_zyy_06: ${ENV(participantId_zyy_06)}
#    participantId_sunyang_06: ${ENV(participantId_sunyang_06)}
#    participantId_zyy_07: ${ENV(participantId_zyy_07)}
#    participantId_sunyang_07: ${ENV(participantId_sunyang_07)}
#    participantId_zyy_08: ${ENV(participantId_zyy_08)}
#    participantId_sunyang_08: ${ENV(participantId_sunyang_08)}
#    participantId_zyy_09: ${ENV(participantId_zyy_09)}
#    participantId_sunyang_09: ${ENV(participantId_sunyang_09)}




testcases:
#  模板发起场景1： 仅仅签署,甲签署,获取加密，获取免登地址:
#    testcase: testcases/openflowtemplate_action/startFlowtemplates_TC/startFlowtemplates_01.yml

  模板发起场景1：仅仅签署,甲签署,获取加密，获取免登地址:
    variables:
      flowTemplateId: ${ENV(template_flowTemplateId01)}
      tenantId1: ${ENV(accountId)}
      tenantId: ${ENV(accountId)}
      accountInfo: {
        "accountInfo":{
          "account": $account,
          "accountName": $accountName,
          "accountOid": $accoutOid,
          "subjectId": $subjectId,
          "subjectName": $subjectName,
          "subjectType": $subjectType
        },
        "preFillValues": {},
        "subTaskName": ""
      }

    testcase: testcases/openflowtemplate_action/startFlowtemplates_TC/startFlowtemplates_01.yml

  模板发起场景2：甲填写签署,甲获取免登地址:
    variables:
      flowTemplateId: ${ENV(template_flowTemplateId02)}
      tenantId1: ${ENV(accountId)}
      tenantId: ${ENV(accountId)}
      accountInfo: {
        "accountInfo":{
          "account": $account,
          "accountName": $accountName,
          "accountOid": $accoutOid,
          "subjectId": $subjectId,
          "subjectName": $subjectName,
          "subjectType": $subjectType
        },
        "preFillValues": {},
        "subTaskName": ""
      }
    testcase: testcases/openflowtemplate_action/startFlowtemplates_TC/startFlowtemplates_01.yml

  模板发起场景3：甲填写,乙签署，甲获取免登地址:
    variables:
      flowTemplateId: ${ENV(template_flowTemplateId03)}
      tenantId1: ${ENV(accountId)}
      tenantId: ${ENV(accountId)}
      userId: zyytest002
      subjectId: ${ENV(accountId)}
      subjectType: 1
      accountInfo: {

        "accountInfo":{
          "account": $account2,
          "accountName": $accountName2,
          "accountOid": $accoutOid2,
          "subjectId": $subjectId2,
          "subjectName": $subjectName2,
          "subjectType": $subjectType2
        },
        "preFillValues": {},
        "subTaskName": ""
      }

      accountInfo1:
        {
          "accountInfo":{
            "account": $account,
            "accountName": $accountName,
            "accountOid": $accoutOid,
            "subjectId": $subjectId,
            "subjectName": $subjectName,
            "subjectType": $subjectType
          },
          "preFillValues": {},
          "subTaskName": ""
        }


    testcase: testcases/openflowtemplate_action/startFlowtemplates_TC/startFlowtemplates_02.yml

  模板发起场景4：甲签署,乙签署，跳过填写地址，甲获取免登地址:
    variables:
      flowTemplateId: ${ENV(template_flowTemplateId04)}
      userId: zyytest002
      tenantId1: ${ENV(accountId)}
      tenantId: ${ENV(accountId)}
      subjectId: ${ENV(accountId)}
      subjectType: 1
      accountInfo: {

        "accountInfo":{
          "account": $account2,
          "accountName": $accountName2,
          "accountOid": $accoutOid2,
          "subjectId": $subjectId2,
          "subjectName": $subjectName2,
          "subjectType": $subjectType2
        },
        "preFillValues": {},
        "subTaskName": ""
      }

      accountInfo1:
        {
          "accountInfo":{
            "account": $account,
            "accountName": $accountName,
            "accountOid": $accoutOid,
            "subjectId": $subjectId,
            "subjectName": $subjectName,
            "subjectType": $subjectType
          },
          "preFillValues": {},
          "subTaskName": ""
        }


    testcase: testcases/openflowtemplate_action/startFlowtemplates_TC/startFlowtemplates_02.yml

  模板发起场景5：甲签署,乙填写，获取乙的免登地址:
    variables:
      flowTemplateId: ${ENV(template_flowTemplateId05)}
      userId: sytest001
      tenantId1: ${ENV(accountId)}
      tenantId: ${ENV(accountId)}
      subjectId: ${ENV(accountId)}
      subjectType: 1
      accountInfo: {

        "accountInfo":{
          "account": $account2,
          "accountName": $accountName2,
          "accountOid": $accoutOid2,
          "subjectId": $subjectId2,
          "subjectName": $subjectName2,
          "subjectType": $subjectType2
        },
        "preFillValues": {},
        "subTaskName": ""
      }

      accountInfo1:
        {
          "accountInfo":{
            "account": $account,
            "accountName": $accountName,
            "accountOid": $accoutOid,
            "subjectId": $subjectId,
            "subjectName": $subjectName,
            "subjectType": $subjectType
          },
          "preFillValues": {},
          "subTaskName": ""
        }


    testcase: testcases/openflowtemplate_action/startFlowtemplates_TC/startFlowtemplates_02.yml

  模板发起场景6：甲签署，乙填写签署，获取乙的免登地址:
    variables:
      flowTemplateId: ${ENV(template_flowTemplateId06)}
      userId: sytest001
      tenantId1: ${ENV(accountId)}
      tenantId: ${ENV(accountId)}
      subjectId: ${ENV(accountId)}
      subjectType: 1
      accountInfo: {

        "accountInfo":{
          "account": $account2,
          "accountName": $accountName2,
          "accountOid": $accoutOid2,
          "subjectId": $subjectId2,
          "subjectName": $subjectName2,
          "subjectType": $subjectType2
        },
        "preFillValues": {},
        "subTaskName": ""
      }

      accountInfo1:
        {
          "accountInfo":{
            "account": $account,
            "accountName": $accountName,
            "accountOid": $accoutOid,
            "subjectId": $subjectId,
            "subjectName": $subjectName,
            "subjectType": $subjectType
          },
          "preFillValues": {},
          "subTaskName": ""
        }


    testcase: testcases/openflowtemplate_action/startFlowtemplates_TC/startFlowtemplates_02.yml

  模板发起场景7：甲填写签署，乙填写签署，获取甲的免登地址:
    variables:
      flowTemplateId: ${ENV(template_flowTemplateId07)}
      tenantId1: ${ENV(accountId)}
      tenantId: ${ENV(accountId)}
      userId: zyytest002
      subjectId: ${ENV(accountId)}
      subjectType: 1
      accountInfo: {

        "accountInfo":{
          "account": $account2,
          "accountName": $accountName2,
          "accountOid": $accoutOid2,
          "subjectId": $subjectId2,
          "subjectName": $subjectName2,
          "subjectType": $subjectType2
        },
        "preFillValues": {},
        "subTaskName": ""
      }

      accountInfo1:
        {
          "accountInfo":{
            "account": $account,
            "accountName": $accountName,
            "accountOid": $accoutOid,
            "subjectId": $subjectId,
            "subjectName": $subjectName,
            "subjectType": $subjectType
          },
          "preFillValues": {},
          "subTaskName": ""
        }


    testcase: testcases/openflowtemplate_action/startFlowtemplates_TC/startFlowtemplates_02.yml

  模板发起场景8：甲填写签署，乙填写签署，获取甲的免登地址:
    variables:
      flowTemplateId: ${ENV(template_flowTemplateId08)}
      tenantId1: ${ENV(accountId)}
      tenantId: ${ENV(accountId)}
      userId: zyytest002
      subjectId: ${ENV(accountId)}
      subjectType: 1
      accountInfo: {

        "accountInfo":{
          "account": $account2,
          "accountName": $accountName2,
          "accountOid": $accoutOid2,
          "subjectId": $subjectId2,
          "subjectName": $subjectName2,
          "subjectType": $subjectType2
        },
        "preFillValues": {},
        "subTaskName": ""
      }

      accountInfo1:
        {
          "accountInfo":{
            "account": $account,
            "accountName": $accountName,
            "accountOid": $accoutOid,
            "subjectId": $subjectId,
            "subjectName": $subjectName,
            "subjectType": $subjectType
          },
          "preFillValues": {},
          "subTaskName": ""
        }


    testcase: testcases/openflowtemplate_action/startFlowtemplates_TC/startFlowtemplates_02.yml

  模板发起场景9：甲填写签署，乙填写，获取甲的免登地址:
    variables:
      flowTemplateId: ${ENV(template_flowTemplateId09)}
      tenantId1: ${ENV(accountId)}
      tenantId: ${ENV(accountId)}
      userId: zyytest002
      subjectId: ${ENV(accountId)}
      subjectType: 1
      accountInfo: {

        "accountInfo":{
          "account": $account2,
          "accountName": $accountName2,
          "accountOid": $accoutOid2,
          "subjectId": $subjectId2,
          "subjectName": $subjectName2,
          "subjectType": $subjectType2
        },
        "preFillValues": {},
        "subTaskName": ""
      }

      accountInfo1:
        {
          "accountInfo":{
            "account": $account,
            "accountName": $accountName,
            "accountOid": $accoutOid,
            "subjectId": $subjectId,
            "subjectName": $subjectName,
            "subjectType": $subjectType
          },
          "preFillValues": {},
          "subTaskName": ""
        }


    testcase: testcases/openflowtemplate_action/startFlowtemplates_TC/startFlowtemplates_02.yml




