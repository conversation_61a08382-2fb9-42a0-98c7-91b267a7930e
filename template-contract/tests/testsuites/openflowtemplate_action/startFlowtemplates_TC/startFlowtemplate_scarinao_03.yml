config:
  name: 批量发起-企业模板发起场景（发起主体是企业）
#  variables:
#    participantId_sunyang_02: ${ENV(participantId_sunyang_02)}
#    participantId_sunyang_03: ${ENV(participantId_sunyang_03)}
#    participantId_zyy_03: ${ENV(participantId_zyy_03)}
#    participantId_sunyang_04: ${ENV(participantId_sunyang_04)}
#    participantId_zyy_04: ${ENV(participantId_zyy_04)}
#    participantId_zyy_05: ${ENV(participantId_zyy_05)}
#    participantId_sunyang_05: ${ENV(participantId_sunyang_05)}
#    participantId_zyy_06: ${ENV(participantId_zyy_06)}
#    participantId_sunyang_06: ${ENV(participantId_sunyang_06)}
#    participantId_zyy_07: ${ENV(participantId_zyy_07)}
#    participantId_sunyang_07: ${ENV(participantId_sunyang_07)}
#    participantId_zyy_08: ${ENV(participantId_zyy_08)}
#    participantId_sunyang_08: ${ENV(participantId_sunyang_08)}
#    participantId_zyy_09: ${ENV(participantId_zyy_09)}
#    participantId_sunyang_09: ${ENV(participantId_sunyang_09)}




testcases:


  企业模板发起场景1：甲填写乙签署批量:
    variables:
      flowTemplateId: ${ENV(template_flowTemplateId_batch)}
      tenantId1: ${ENV(accountId)}
      tenantId: ${ENV(accountId)}
#      userId: zyytest002
      subjectId: ${ENV(accountId)}
      subjectType: 1
      accountInfo: {

          "accountInfo":{
            "account": $account2,
            "accountName": $accountName2,
            "accountOid": $accoutOid2,
            "subjectId": $subjectId2,
            "subjectName": $subjectName2,
            "subjectType": $subjectType2
          },
          "preFillValues": {},
          "subTaskName": ""
        }

      accountInfo1:
        {
            "accountInfo":{
              "account": $account,
              "accountName": $accountName,
              "accountOid": $accoutOid,
              "subjectId": $subjectId,
              "subjectName": $subjectName,
              "subjectType": $subjectType
            },
            "preFillValues": {},
            "subTaskName": ""
          }



    testcase: testcases/openflowtemplate_action/startFlowtemplates_TC/startFlowtemplates_05.yml





