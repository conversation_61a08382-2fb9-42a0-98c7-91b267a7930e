config:
  name: 轩辕api直接发起接口
  base_url: ${ENV(xy_open_url)}

testcases:
  发起接口参数校验:
    testcase: testcases/openflowtemplate_action/start_process.yml


  场景1-企业类型印章不指定,获取A操作地址，获取B企业操作地址,获取免登地址:

     testcase: testcases/openflowtemplate_action/getProcessurl_scenario/getProcessUrl_action1.yml

  场景2-企业类型印章：企业印章,获取A操作地址，获取B企业操作地址,获取免登地址:
    testcase: testcases/openflowtemplate_action/getProcessurl_scenario/getProcessUrl_action2.yml

  场景3-企业类型印章：法人章,获取A操作地址，获取B企业操作地址,获取免登地址:
    testcase: testcases/openflowtemplate_action/getProcessurl_scenario/getProcessUrl_action3.yml

  场景4-企业类型印章：企业印章+法人章,获取A操作地址，获取B企业操作地址,获取免登地址:
    testcase: testcases/openflowtemplate_action/getProcessurl_scenario/getProcessUrl_action4.yml

  场景5-个人印章：个人章不传,获取A操作地址，获取B企业操作地址,获取免登地址:
    testcase: testcases/openflowtemplate_action/getProcessurl_scenario/getProcessUrl_action5.yml

  场景6-个人印章：个人手绘,获取A操作地址，获取B企业操作地址,获取免登地址:
    testcase: testcases/openflowtemplate_action/getProcessurl_scenario/getProcessUrl_action6.yml

  场景7-个人印章：个人模板,获取A操作地址，获取B企业操作地址,获取免登地址:
    testcase: testcases/openflowtemplate_action/getProcessurl_scenario/getProcessUrl_action7.yml

  场景8-企业类型印章：个人手绘+模板，模板印章,获取A操作地址，获取B企业操作地址,获取免登地址:
    testcase: testcases/openflowtemplate_action/getProcessurl_scenario/getProcessUrl_action8.yml

  场景9-批量发起:直接发起个人主体批量发起:
    testcase: testcases/openflowtemplate_action/start_process_TC02.yml







