config:
  name: 判断是否可以发起签署

testcases:
-
  name: 判断是否可以发起签署 -$CaseName
  testcase: testcases/sharetasks/canStartSign_action.yml
  parameters:
  - CaseName-accountId-shareTaskId-code1-message1-message2:
    - ["可以发起签署（余额充足）","${ENV(accountId)}","${ENV(shareTaskId)}",0,"成功",True]
    - ["accountId为空","","${ENV(shareTaskId)}",0,"成功",True]
    - ["shareTaskId为空","${ENV(accountId)}","",0,"成功",False]
    - ["shareTaskId不存在","${ENV(accountId)}","ST-7347326473264721428784219741983913",0,"成功",False]