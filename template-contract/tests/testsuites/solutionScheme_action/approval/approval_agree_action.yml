config:
  name: 解决方案审核

testcases:

  -
    name: 解决方案审核-$CaseName
    testcase: testcases/solutionScheme/approval/approval_agree_action.yml
    parameters:
      - CaseName-approvalId-approvalReason-evidenceType-fileKey-fileName-code-message:
          - ["approvalId为空","","审核",0,"${ENV(fileKey_realname)}","${ENV(fileName_realname)}",31202000,"审核id不能为空"]
          - ["approvalId不存在","123","审核",0,"${ENV(fileKey_realname)}","${ENV(fileName_realname)}",312100009,"解决方案审核记录不存在"]
          - ["approvalReason为空","123","",0,"${ENV(fileKey_realname)}","${ENV(fileName_realname)}",31202000,"审核原因不能为空"]
          - ["approvalReason>200","123","123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901",0,"${ENV(fileKey_realname)}","${ENV(fileName_realname)}",31202000,"审核原因最大不能超出200个字"]
          - ["evidenceType为空","123","审核",null,"${ENV(fileKey_realname)}","${ENV(fileName_realname)}",31202000,"请指定出证类型"]
          - ["evidenceType不合法（0和1以外的值）","123","审核",2,"${ENV(fileKey_realname)}","${ENV(fileName_realname)}",312100000,"出证类型无效"]
          - ["fileKey为空","123","审核",0,"","${ENV(fileName_realname)}",312100000,"安全认可附件参数缺失"]
          - ["fileName为空","123","审核",0,"${ENV(fileKey_realname)}","",312100000,"安全认可附件参数缺失"]

  -
    name: 支持可信场景实名的解决方案审核及重新审核，设置线下手动出证
    testcase: testcases/solutionScheme/approval/approval_agree_action1.yml


  -
    name: 不支持可信场景实名的解决方案审核及重新审核，设置线上自动出证
    testcase: testcases/solutionScheme/approval/approval_agree_action2.yml
