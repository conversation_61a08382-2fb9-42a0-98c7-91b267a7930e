config:
    name: 获取解决方案审核列表

testcases:
    -
        name: 获取解决方案审核列表-$CaseName
        testcase: testcases/solutionScheme/approval/approval_list_action.yml
        parameters:
            - CaseName-pageSize-pageIndex-serialNumber-code-message:
                  - ["获取解决方案审核列表",10,1,"",0,"成功"]
                  - ["获取解决方案审核列表，pageSize为-1",-1,1,"",31202000,"参数错误: pageSize不能小于1"]
                  - ["获取解决方案审核列表，pageSize为100",100,1,"",0,"成功"]
                  - ["获取解决方案审核列表，pageSize为101",101,1,"",31202000,"参数错误: pageSize最大仅支持100"]
                  - ["获取解决方案审核列表，pageIndex为-1",10,-1,"",0,"成功"]
                  - ["获取解决方案审核列表，pageIndex为0",10,0,"",0,"成功"]
                  - ["获取解决方案审核列表，serialNumber为精确查询",10,1,"${ENV(serialNumber_thirdrealname)}",0,"成功"]
