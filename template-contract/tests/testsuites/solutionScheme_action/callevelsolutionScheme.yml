config:
    name: 计算分值

testcases:
-
    name: 计算分值-$<PERSON><PERSON>ame
    testcase: testcases/solutionScheme/callevelsolutionScheme.yml
    parameters:
      - CaseName-Bscore-logincore-Rcode-Tscore-Wcode-code1-message1:
          - ["安全分为空","",true,"0200",60,"0200",31202000,"参数错误"]
          - ["免登设置参数为空",60,"","0200",60,"0200",31202000,"参数错误"]
          - ["安全等级为低级",0,true,"0200",60,"0200",0,"成功"]
          - ["安全等级为中级",50,true,"0200",60,"0100",0,"成功"]
          - ["安全等级为高级",90,true,"0100",100,"0100",0,"成功"]






