config:
    name: 添加解决方案

testcases:
-
    name: 添加解决方案-$CaseName
    testcase: testcases/solutionScheme/createsolutionScheme.yml
    parameters:
      - CaseName-developerOid-ssname-ssdesc-easyLogin-wlevelCode-woptions-rlevelCode-poptions-ooptions-tscore-bscore-realNameType-thirdExtend-code1-message1:
          - ["developerOid为空","","test-sosu-1","新增解决方案",true,"0200","2,3,4,5,6","0200","2,3","2,3,1","90","90",1,null,31202000,"参数错误"]
          - ["developerOid不存在或注销","69ewgqerewtweterwr157","test-sosu-1","新增解决方案",true,"0200","2,3,4,5,6","0200","2,3","2,3,1","80","80",1,null,312030000,"账号不存在或已注销"]
          - ["方案名称为空","${ENV(orgid2)}","","新增解决方案",true,"0200","2,3,4,5,6","0200","2,3","2,3,1","80","90",1,null,31202000,"参数错误"]
          - ["方案名称长度大于30字符","${ENV(orgid2)}","方案名称长度大于30字符方案名称长度大于30字符方案名称长度大","新增解决方案",true,"0200","2","0200","2","2","80","90",1,null,31202000,"参数错误: 解决方案名称长度为1~30个字符"]
          - ["方案描述为空","${ENV(orgid2)}","test-sosu-2","",true,"0200","2,3,4,5,6","0200","2,3","2,3,1","80","90",1,null,31202000,"参数错误"]
          - ["方案描述大于100字符","${ENV(orgid2)}","test-sosu-2","12345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901",true,"0200","2,3,4,5,6","0200","2,3","2,3,1","80","90",1,null,31202000,"参数错误: 解决方案描述长度为1~100个字符"]
          - ["意愿等级编码为空","${ENV(orgid2)}","test-sosu-3","新增解决方案",true,"","2","0200","2","2","80","90",1,null,31202000,"参数错误: 意愿等级编码长度不正确"]
          - ["意愿等级编码不存在","${ENV(orgid2)}","test-sosu-3","新增解决方案",true,"66666","2","0200","2","2","80","90",1,null,312100000,"参数错误: 意愿等级编码错误"]
          - ["意愿开启项为空","${ENV(orgid2)}","test-sosu-3","新增解决方案",true,"0200","","0200","2","2","80","90",1,null,31202000,"参数错误: 意愿配置信息长度不正确"]
          - ["意愿开启项不存在","${ENV(orgid2)}","test-sosu-3","新增解决方案",true,"0200","66","0200","2","2","80","90",1,null,312100000,"参数错误"]
          - ["实名等级编码为空","${ENV(orgid2)}","test-sosu-3","新增解决方案",true,"0200","2","","2","2","80","90",1,null,31202000,"参数错误: 实名等级编码长度不正确"]
          - ["实名等级编码不存在","${ENV(orgid2)}","test-sosu-3","新增解决方案",true,"0200","2","666666","2","2","80","90",1,null,312100000,"参数错误: 实名等级编码错误"]
          - ["个人实名开启项为空","${ENV(orgid2)}","test-sosu-3","新增解决方案",true,"0200","2","0200","","2","80","90",1,null,31202000,"参数错误: 个人实名配置信息长度不正确"]
          - ["个人实名开启项不存在","${ENV(orgid2)}","test-sosu-3","新增解决方案",true,"0200","2","0200","6666","2","80","90",1,null,312100000,"参数错误"]
          - ["企业实名开启项为空","${ENV(orgid2)}","test-sosu-3","新增解决方案",true,"0200","2","0200","2","","80","90",1,null,31202000,"参数错误: 企业实名配置信息长度不正确"]
          - ["企业实名开启项不存在","${ENV(orgid2)}","test-sosu-3","新增解决方案",true,"0200","2","0200","2","6666","80","90",1,null,312100000,"参数错误"]
          - ["技术安全分为空","${ENV(orgid2)}","test-sosu-4","新增解决方案",true,"0200","2","0200","2","2","","90",1,null,31202000,"参数错误: 安全报告分值不能为空"]
          - ["技术安全分小于0","${ENV(orgid2)}","test-sosu-4","新增解决方案",true,"0200","2","0200","2","2","-1","90",1,null,31202000,"参数错误: 安全报告分值最低为0分"]
          - ["技术安全分大于100","${ENV(orgid2)}","test-sosu-4","新增解决方案",true,"0200","2","0200","2","2","101","90",1,null,31202000,"参数错误: 安全报告分值最高为100分"]
          - ["业务安全分为空","${ENV(orgid2)}","test-sosu-4","新增解决方案",true,"0200","2","0200","2","2","0","",1,null,31202000,"参数错误: 安全报告分值不能为空"]
          - ["业务安全分小于0","${ENV(orgid2)}","test-sosu-4","新增解决方案",true,"0200","2","0200","2","2","0","-1",1,null,31202000,"参数错误: 安全报告分值最低为0分"]
          - ["业务安全分大于100","${ENV(orgid2)}","test-sosu-4","新增解决方案",true,"0200","2","0200","2","2","100","101",1,null,31202000,"参数错误: 安全报告分值最高为100分"]
          - ["正常新增解决方案","${ENV(orgid2)}","solu-${getTimeStamp()}","新增解决方案",true,"0200","2,3,4,5,6","0200","2,3,4,5,6","1,3,4,5,2","100","100",1,null,0,"成功"]







