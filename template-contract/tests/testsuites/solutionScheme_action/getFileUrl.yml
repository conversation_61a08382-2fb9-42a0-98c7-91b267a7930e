config:
    name: 获取安全报告文件URL

testcases:
-
    name: 获取安全报告文件URL-$CaseName
    testcase: testcases/solutionScheme/getFileUrl.yml
    parameters:
      - CaseName-LdeveloperOid-pageSize-pageIndex-totalResult-matchName-code1-message1-fileKey-code2-message2:
          - ["fileKey为空","${ENV(orgid)}",1,1,true,"",0,"成功","",312100004,"解决方案文件不存在"]
          - ["非当前方案fileKey","${ENV(orgid)}",1,1,false,"",0,"成功","${ENV(filekey1)}",312100004,"解决方案文件不存在"]
          - ["正常获取报告url","${ENV(orgid)}",1,1,false,"",0,"成功","${ENV(filekey2)}",0,"成功"]
          - ["fileKey不存在","${ENV(orgid)}",1,1,false,"",0,"成功","hdfhjf7585",312100004,"解决方案文件不存在"]







