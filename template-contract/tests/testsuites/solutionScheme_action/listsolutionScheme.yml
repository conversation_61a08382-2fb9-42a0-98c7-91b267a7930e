config:
    name: 获取解决方案列表

testcases:
-
    name: 获取解决方案列表-$CaseName
    testcase: testcases/solutionScheme/listsolutionScheme.yml
    parameters:
      - CaseName-LdeveloperOid-pageSize-pageIndex-totalResult-matchName-code1-message1:
          - ["developerOid为空","",2,1,true,"",0,"成功"]
          - ["developerOid不为空","${ENV(orgid)}",1,1,true,"",0,"成功"]
          - ["totalResult为false不显示总数","${ENV(orgid)}",1,1,false,"",0,"成功"]






