config:
  name: 推送个人三方实名数据


testcases:
  -
    name: 推送个人三方实名数据-$CaseName
    testcase: testcases/solutionScheme/push_psnrealname_action.yml
    parameters:
      - CaseName-appId-accountOid-certType-certNo-mobileNo-name-code-message:
          - ["大陆个人三方实名数据","${ENV(appid_thirdrealname)}","8723abe263f142009d98471ce7a70bd8","INDIVIDUAL_CH_IDCARD","******************","***********","谢佳",0,"成功"]
          - ["非大陆个人三方实名数据","${ENV(appid_thirdrealname)}","8723abe263f142009d98471ce7a70bd8","INDIVIDUAL_PASSPORT","*********","***********","谢佳",0,"成功"]
          - ["appId为空","","8723abe263f142009d98471ce7a70bd8","INDIVIDUAL_CH_IDCARD","******************","***********","谢佳",*********,"应用不存在"]
          - ["accountOid为空","${ENV(appid_thirdrealname)}","","INDIVIDUAL_CH_IDCARD","******************","***********","谢佳",********,"缺少参数：oid"]
          - ["certType为空","${ENV(appid_thirdrealname)}","8723abe263f142009d98471ce7a70bd8","","******************","***********","谢佳",********,"缺少参数：certType"]
          - ["certNo为空","${ENV(appid_thirdrealname)}","8723abe263f142009d98471ce7a70bd8","INDIVIDUAL_CH_IDCARD","","***********","谢佳",********,"缺少参数：certNo"]
          - ["mobileNo为空","${ENV(appid_thirdrealname)}","8723abe263f142009d98471ce7a70bd8","INDIVIDUAL_CH_IDCARD","******************","","谢佳",********,"缺少参数：mobileNo"]
          - ["name为空","${ENV(appid_thirdrealname)}","8723abe263f142009d98471ce7a70bd8","INDIVIDUAL_CH_IDCARD","******************","***********","",********,"缺少参数：name"]
          - ["certType值无效","${ENV(appid_thirdrealname)}","8723abe263f142009d98471ce7a70bd8","INDIVIDUAL_CH_IDCARD1","******************","***********","谢佳",********,"参数错误: certType无效"]
          - ["大陆个人三方实名数据，certNo不正确","${ENV(appid_thirdrealname)}","8723abe263f142009d98471ce7a70bd8","INDIVIDUAL_CH_IDCARD","******************","***********","谢佳",********,"参数错误：请输入正确的身份证号码!"]
          - ["mobileNo格式不正确","${ENV(appid_thirdrealname)}","8723abe263f142009d98471ce7a70bd8","INDIVIDUAL_CH_IDCARD","******************","***********0","谢佳",********,"参数错误：手机号格式不正确"]
          - ["name少于2个字符","${ENV(appid_thirdrealname)}","8723abe263f142009d98471ce7a70bd8","INDIVIDUAL_CH_IDCARD","******************","***********","佳",********,"参数错误：姓名长度2~20位"]
          - ["name超过20个字符","${ENV(appid_thirdrealname)}","8723abe263f142009d98471ce7a70bd8","INDIVIDUAL_CH_IDCARD","******************","***********","谢佳谢佳谢佳谢佳谢佳谢佳谢佳谢佳谢佳谢佳谢",********,"参数错误：姓名长度2~20位"]
          - ["大陆个人三方实名数据，name格式不正确","${ENV(appid_thirdrealname)}","8723abe263f142009d98471ce7a70bd8","INDIVIDUAL_CH_IDCARD","******************","***********","谢佳+",********,"参数错误：姓名只支持中文/点符号·"]
          - ["非大陆个人三方实名数据，name格式不正确","${ENV(appid_thirdrealname)}","8723abe263f142009d98471ce7a70bd8","INDIVIDUAL_PASSPORT","******************","***********","谢佳1",********,"参数错误：姓名只支持中文/大小写英文/点符号·/破折号-/半角括号"]
