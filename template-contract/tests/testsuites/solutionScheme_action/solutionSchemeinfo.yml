config:
    name: 获取解决方案详情

testcases:
-
    name: 获取解决方案详情-$CaseName
    testcase: testcases/solutionScheme/solutionSchemeinfo.yml
    parameters:
      - CaseName-LdeveloperOid-pageSize-pageIndex-totalResult-matchName-code1-message1-IdeveloperOid-code2-message2:
          - ["IdeveloperOid为空","${ENV(orgid)}",1,1,true,"",0,"成功","",312100000,"开发者oid不能为空"]
          - ["企业开发者不匹配","${ENV(orgid)}",1,1,true,"",0,"成功","${ENV(ouid)}",312100003,"企业开发者不匹配"]
          - ["企业开发者不存在","${ENV(orgid)}",1,1,true,"",0,"成功","xxxx8798ghj",312100003,"解决方案无权访问"]
          - ["正常获取解决方案详情","${ENV(orgid)}",1,1,true,"",0,"成功","${ENV(orgid)}",0,"成功"]





