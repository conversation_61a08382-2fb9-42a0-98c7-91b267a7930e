config:
    name: 更新解决方案安全报告文件

testcases:
-
    name: 更新解决方案安全报告文件-$CaseName
    testcase: testcases/solutionScheme/updateSafetyReportFile.yml
    parameters:
      - CaseName-LdeveloperOid-pageSize-pageIndex-totalResult-matchName-code1-message1-UPdeveloperOid-BfileKey-TfileKey-code2-message2:
          - ["fileKey为空","${ENV(orgid2)}",1,1,true,"",0,"成功","${ENV(orgid2)}","","",312100000,"参数错误"]
          - ["企业开发者oid为空","${ENV(orgid2)}",1,1,true,"",0,"成功","","${ENV(filekey1)}","${ENV(filekey1)}",31202000,"参数错误"]
          - ["企业开发者不匹配","${ENV(orgid2)}",1,1,true,"",0,"成功","${ENV(ouid)}","${ENV(filekey1)}","${ENV(filekey1)}",312100003,"解决方案无权访问"]
          - ["正常更新方案报告","${ENV(orgid2)}",1,1,true,"",0,"成功","${ENV(orgid2)}","${ENV(filekey1)}","${ENV(filekey1)}",0,"成功"]





