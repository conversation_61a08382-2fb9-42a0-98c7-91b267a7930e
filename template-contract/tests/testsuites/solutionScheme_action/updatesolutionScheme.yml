config:
    name: 编辑解决方案

testcases:
-
    name: 编辑解决方案-$CaseName
    testcase: testcases/solutionScheme/updatesolutionScheme.yml
    parameters:
      - CaseName-LdeveloperOid-pageSize-pageIndex-totalResult-matchName-code1-message1-EdeveloperOid-enable-code2-message2-developerOid-ssname-ssdesc-easyLogin-wlevelCode-woptions-rlevelCode-poptions-ooptions-tscore-bscore-code3-message3:
          - ["developerOid为空","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","","test-bianjisu-1","新增解决方案",true,"0200","2,3,4,5,6","0200","2,3","2,3,1","90","90",31202000,"参数错误"]
          - ["developerOid无权限编辑","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(ouid)}","test-bianji-1","新增解决方案",true,"0200","2,3,4,5,6","0200","2,3","2,3,1","60","90",312100003,"解决方案无权访问"]
          - ["Oid无权限开启解决方案","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid2)}",false,312100003,"解决方案无权访问","${ENV(ouid)}","test-bianji-1","新增解决方案",true,"0200","2,3,4,5,6","0200","2,3","2,3,1","60","90",312100003,"解决方案无权访问"]
          - ["解决方案开启状态无法编辑","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",true,0,"成功","${ENV(orgid)}","test-bianji-1","新增解决方案",true,"0200","2,3,4,5,6","0200","2,3","2,3,1","60","90",312100002,"解决方案未禁用"]
          - ["编辑解决方案名称为空","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","","新增解决方案",true,"0200","2,3,4,5,6","0200","2,3","2,3,1","60","90",31202000,"参数错误"]
          - ["编辑解决方案描述为空","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test-bianji-1","",true,"0200","2,3,4,5,6","0200","2,3","2,3,1","60","90",31202000,"参数错误"]
          - ["编辑解决方案easyLogin为空","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test-bianji-1","solution","","0200","2,3,4,5,6","0200","2,3","2,3,1","60","90",31202000,"免登开关必须为true或者false"]
          - ["编辑解决方案名称大于30字符","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test-bianji-1%￥#&*（）解决方案1234567","solution",true,"0200","2,3,4,5,6","0200","2,3","2,3,1","60","90",31202000,"名称长度为1~30个字符"]
          - ["编辑意愿等级编码为空","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test1","solution",false,"","2,3,4,5,6","0200","2,3","2,3,1","60","90",31202000,"意愿等级编码长度不正确"]
          - ["编辑意愿等级编码不存在","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test1","solution",false,"0925","2,3,4,5,6","0200","2,3","2,3,1","60","90",312100000,"意愿等级编码错误"]
          - ["编辑意愿等级开启项为空","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test1","solution",false,"0200","","0200","2,3","2,3,1","60","90",31202000,"参数错误"]
          - ["编辑意愿等级编码与开启项不匹配","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test1","solution",false,"0100","3,4","0200","2,3","2,3,1","60","90",312100000,"意愿认证方式不匹配"]
          - ["编辑实名等级编码为空","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test1","solution",false,"0200","6,3","","2,3","2,3,1","60","90",31202000,"参数错误"]
          - ["编辑实名等级编码不存在","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test1","solution",false,"0200","6,3","0925","2,3","2,3,1","60","90",312100000,"参数错误"]
          - ["编辑个人实名等级编码与开启项不匹配","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test1","solution",false,"0200","6,3","0100","2,3","2,1","60","90",312100000,"个人实名认证方式不匹配"]
          - ["编辑企业实名等级编码与开启项不匹配","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test1","solution",false,"0200","6,3","0100","3","2,1,3","60","90",312100000,"企业实名认证方式不匹配"]
          - ["编辑人工审核方式-未配置相应意愿方式1","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test1","solution",false,"0100","2","0200","2,5","2,1,3","60","90",312100007,"意愿须至少选择“短信意愿”和“密码意愿"]
          - ["编辑人工审核方式-未配置相应意愿方式2","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test1","solution",false,"0200","2,4,5","0200","2,5","2,5,3","60","90",312100007,"意愿须至少选择“短信意愿”和“密码意愿"]
          - ["编辑实名开启项为空1","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test1","solution",false,"0200","2,4,5","0200","2","","60","90",31202000,"参数错误"]
          - ["编辑实名开启项为空2","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test1","solution",false,"0200","2,4,5","0200","","2","60","90",31202000,"参数错误"]
          - ["编辑安全分为空","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test1","solution",false,"0200","2,4,5","0200","2","2","","90",31202000,"安全报告分值不能为空"]
          - ["编辑安全分小于0","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test1","solution",false,"0200","2,4,5","0200","2","2","-1","90",31202000,"安全报告分值最低为0分"]
          - ["编辑安全分大于100","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test1","solution",false,"0200","2,4,5","0200","2","2","0","101",31202000,"安全报告分值最高为100分"]
          - ["编辑方案安全等级为高级","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test1-so","solution",false,"0200","2,4,5","0200","2","2","100","100",0,"成功"]
          - ["编辑方案安全等级为中级","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test2-so","solution",true,"0100","2,4","0100","3,4","2","60","60",0,"成功"]
          - ["编辑方案安全等级为低级","${ENV(orgid)}",2,1,true,"",0,"成功","${ENV(orgid)}",false,0,"成功","${ENV(orgid)}","test3-so","solution",true,"0100","2,4","0100","3,4","1,2","0","60",0,"成功"]







