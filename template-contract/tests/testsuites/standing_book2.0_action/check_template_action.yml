config:
    name: 智能台账2.0-流程模板是否绑定台账规则

testcases:
-
    name: 查询流程模板是否已经绑定台账规则-$CaseName

    testcase: testcases/standing_book2.0_action/check_template.yml
    parameters:
      - CaseName-accountId-tenantId-flowTemplate-code-message:
          - ["accountId为空","","${ENV(orgid)}","${ENV(flowTemplateId1)}",0,"成功"]
          - ["accountId不存在","dvgiuwiuejej6574748","${ENV(orgid)}","${ENV(flowTemplateId1)}",0,"成功"]
          - ["accountId与租户id不匹配","${ENV(accountId)}","${ENV(orgid2)}","${ENV(flowTemplateId1)}",0,"成功"]
          - ["flowTemplateId为空","${ENV(accountId)}","${ENV(orgid2)}","",0,"成功"]
          - ["flowTemplateId不存在","${ENV(accountId)}","${ENV(orgid2)}","xxx3343632",0,"成功"]
          - ["flowTemplateId已删除","${ENV(accountId)}","${ENV(orgid2)}","${ENV(flowTemplateId_delete)}",0,"成功"]
          - ["流程绑定了台账规则","${ENV(accountId)}","${ENV(orgid2)}","${ENV(flowTemplateId1)}",0,"成功"]
          - ["流程未绑定台账规则","${ENV(accountId)}","${ENV(orgid2)}","${ENV(flowTemplateId2)}",0,"成功"]
