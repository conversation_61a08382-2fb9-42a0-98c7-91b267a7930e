config:
    name: 智能台账2.0-上传台账规则模板

testcases:
-
    name: 智能台账2.0-上传台账规则模板-$CaseName
    testcase: testcases/standing_book2.0_action/create_templateruleId_action.yml
    parameters:
      - CaseName-accountId-tenantId-fileId-ruleTemplateId-flowTemplateId-code1-message1:
          - ["accountId为空","","${ENV(orgid)}","","","${ENV(flowTemplateId2)}",********,"无操作权限"]
          - ["accountId不存在","dvgiuwiuejej6574748","${ENV(orgid)}","","","${ENV(flowTemplateId2)}",********,"无操作权限"]
          - ["accountId与租户id不匹配","${ENV(accountId)}","${ENV(orgid2)}","","","${ENV(flowTemplateId2)}",********,"无操作权限"]
          - ["fileId与flowTemplateId都为空","${ENV(ouid)}","${ENV(orgid2)}","","","",********,"不能为空"]
          - ["fileId与模板相似度达到阈值","${ENV(ouid)}","${ENV(orgid2)}","${ENV(fileId_yucheng)}","${ENV(ruleTemplateId_yucheng)}","",********,"该文件已上传，不可重复上传"]
          - ["flowTemplateId不存在","${ENV(ouid)}","${ENV(orgid2)}","","","nulltemplateid",*********,"流程模板不存在"]
          - ["非当前企业下的flowTemplateId","${ENV(ouid)}","${ENV(orgid2)}","","","${ENV(flowTemplateId3)}",*********,"流程模板不属于该账号"]
          - ["正常上传流程模板获取规则id","${ENV(ouid)}","${ENV(orgid2)}","","","${ENV(flowTemplateId1)}",0,"成功"]
          - ["非当前企业下的fileId","${ENV(ouid)}","${ENV(orgid2)}","${ENV(fileId)}","","",********,"用户没有操作权限"]
          - ["fileId不存在","${ENV(ouid)}","${ENV(orgid2)}","2222#3536dsf","","",********,"合同不存在"]
#          - ["正常上传本地模板获取规则id","${ENV(ouid)}","${ENV(orgid2)}","${ENV(fileId_yucheng)}",null,"",0,"成功"]
