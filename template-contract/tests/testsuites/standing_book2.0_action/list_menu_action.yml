config:
    name: 智能台账2.0-获取分类列表

testcases:
-
    name: 智能台账2.0-获取分类列表-$CaseName

    testcase: testcases/standing_book2.0_action/list_menu_action.yml
    parameters:
      - CaseName-ruleId-accountId-tenantId-code-message:
          - ["accountId为空","","","${ENV(orgid)}",********,"缺少参数"]
          - ["accountId不存在","","dvgiuwiuejej6574748","${ENV(orgid)}",*********,"账号不存在或已注销"]
          - ["accountId传企业id","","${ENV(orgid)}","${ENV(orgid2)}",*********,"企业成员不存在"]
#          - ["租户id为空","","${ENV(ouid)}","",*********,"账号不存在或已注销"]
          - ["租户id不存在","","${ENV(ouid)}","xxxxnoexist",*********,"账号不存在或已注销"]
          - ["accountId与租户id不匹配","","${ENV(accountId)}","${ENV(orgid2)}",*********,"企业成员不存在"]
          - ["正常获取分类列表","","${ENV(ouid)}","${ENV(orgid2)}",0,"成功"]
