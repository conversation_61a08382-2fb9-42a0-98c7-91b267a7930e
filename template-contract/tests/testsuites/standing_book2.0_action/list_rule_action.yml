config:
    name: 智能台账2.0-查询台账规则列表

testcases:
-
    name: 智能台账2.0-查询台账规则列表-$CaseName

    testcase: testcases/standing_book2.0_action/list_rule_action.yml
    parameters:
      - CaseName-accountId-tenantId-pageNum-pageSize-code-message:
          - ["accountId为空","","${ENV(orgid)}",1,10,0,"成功"]
          - ["accountId不存在","dvgiuwiuejej6574748","${ENV(orgid)}",1,10,0,"成功"]
          - ["accountId传企业oid","${ENV(orgid)}","${ENV(orgid2)}",1,10,0,"成功"]
          - ["租户id为空","${ENV(ouid)}","",1,10,0,"成功"]
          - ["租户id为个人oid","${ENV(ouid)}","${ENV(ouid2)}",1,10,0,"成功"]
          - ["accountId与租户id不匹配","${ENV(accountId)}","${ENV(orgid2)}",1,10,0,"成功"]
          - ["pageNum传入非数字","${ENV(accountId)}","${ENV(orgid2)}",1,10,0,"成功"]
          - ["pageNum传入空","${ENV(accountId)}","${ENV(orgid2)}","",10,0,"成功"]
          - ["pageSize传入非数字","${ENV(accountId)}","${ENV(orgid2)}",1,10,0,"成功"]
          - ["pageSize传入空","${ENV(accountId)}","${ENV(orgid2)}",1,"",0,"成功"]
          - ["正常查询台账规则列表","${ENV(ouid)}","${ENV(orgid2)}",1,10,0,"成功"]
