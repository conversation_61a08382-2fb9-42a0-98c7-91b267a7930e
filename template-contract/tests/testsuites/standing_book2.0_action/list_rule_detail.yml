config:
    name: 智能台账2.0-查询台账规则详情

testcases:
-
    name: 智能台账2.0-查询台账规则详情-$CaseName

    testcase: testcases/standing_book2.0_action/list_rule_detail.yml
    parameters:
      - CaseName-ruleId-accountId-tenantId-code-message:
          - ["accountId为空","${ENV(ruleId_other)}","","${ENV(orgid2)}",********,"无操作权限"]
          - ["accountId不存在","${ENV(ruleId_other)}","dvgiuwiuejej6574748","${ENV(orgid2)}",********,"无操作权限"]
          - ["accountId与租户id不匹配","${ENV(ruleId_other)}","${ENV(accountId)}","${ENV(orgid2)}",********,"无操作权限"]
          - ["租户id为空","${ENV(ruleId_other)}","${ENV(ouid)}","",********,"缺少参数: orgId"]
          - ["租户id不存在","${ENV(ruleId_other)}","${ENV(ouid)}","xxxxnoexist",*********,"账号不存在或已注销"]
          - ["accountId传企业oid","${ENV(ruleId_other)}","${ENV(orgid2)}","${ENV(orgid2)}",********,"无操作权限"]
          - ["ruleId为空","","${ENV(ouid)}","${ENV(orgid2)}",********,"台账规则不存在"]
          - ["ruleId不存在","xxx12338y2jkkj","${ENV(ouid)}","${ENV(orgid2)}",********,"台账规则不存在"]
          - ["非当前企业下创建的ruleId","${ENV(ruleId_other)}","${ENV(ouid)}","${ENV(orgid2)}",********,"台账规则不存在"]
