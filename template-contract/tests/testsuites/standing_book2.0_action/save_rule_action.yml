config:
    name: 智能台账2.0-保存台账规则

testcases:
-
    name: 新增一条生效的台账规则seName
    testcase: testcases/standing_book2.0_action/create_effect_rule.yml

-
    name: 保存台账规则
    testcase: testcases/standing_book2.0_action/save_rule_action.yml

-
    name: 关联扩展字段
    testcase: testcases/standing_book2.0_action/relevance_rule_action.yml

-
    name: 编辑台账规则
    testcase: testcases/standing_book2.0_action/edit_rule_action.yml