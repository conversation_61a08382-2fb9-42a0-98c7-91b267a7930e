config:
    name: 解析合同模板2

testcases:
-
    name: 解析合同模板2-$CaseName
    testcase: testcases/standing_book/analysis_action2.yml
    parameters:
      - CaseName-fileKey-accountId-analyseTenantId-code1-message1:
          - ["合同无解析规则","${ENV(filekey2)}","${ENV(ouid)}","${ENV(orgid2)}",0,"成功"]
          - ["企业成员解析合同模板","${ENV(filekey1)}","${ENV(ouid2)}","${ENV(orgid2)}",********,"无操作权限"]
          - ["accountId为非当前操作人","${ENV(filekey1)}","${ENV(orgid2)}","${ENV(orgid2)}",********,"无操作权限"]


