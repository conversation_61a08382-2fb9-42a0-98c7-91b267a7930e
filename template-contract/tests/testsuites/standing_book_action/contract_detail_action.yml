config:
    name: 导出合同明细

testcases:
-
    name: 导出合同明细-$CaseName
    testcase: testcases/standing_book/contract_detail_action.yml
    parameters:
      - CaseName-detailoid-detailTenantId-code1-message1-message2:
          - ["导出个人下已归档合同明细","${ENV(ouid)}","${ENV(ouid)}",0,"成功","https://esignoss.esign.cn"]
          - ["导出企业下已归档合同明细","${ENV(ouid)}","${ENV(orgid2)}",0,"成功","https://esignoss.esign.cn"]
