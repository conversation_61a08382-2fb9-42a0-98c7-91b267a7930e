config:
    name: 创建台账规则

testcases:
-
    name: 创建台账规则-$CaseName
    testcase: testcases/standing_book/create_rule_action.yml
    parameters:
      - CaseName-accountId-fileKey-example-name-property-type-ruleId-createruleTenantId-code1-message1:
#          - ["正常创建台账规则","${ENV(ouid)}","${ENV(filekey1)}","孔明","甲方姓名","pers","姓名","","${ENV(orgid2)}",0,"成功"]
          - ["accountId非当前操作人","${ENV(ouid2)}","${ENV(filekey1)}","孔明","甲方姓名","pers","姓名","","${ENV(orgid2)}",********,"无操作权限"]
          - ["非企业管理员创建台账规则","${ENV(ouid)}","${ENV(filekey1)}","孔明","甲方姓名","pers","姓名","","${ENV(orgid4)}",********,"无操作权限"]
          - ["添加ai解析字段为空","${ENV(ouid)}","${ENV(filekey2)}","","","","电话","","${ENV(orgid2)}",********,"无法解析该字段"]