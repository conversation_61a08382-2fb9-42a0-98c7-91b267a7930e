config:
    name: 创建台账规则2

testcases:
-
    name: 创建台账规则2-$CaseName
    testcase: testcases/standing_book/create_rule_action2.yml
    parameters:
      - CaseName-accountId-fileKey-example1-name1-property1-type1-example2-name2-property2-type2-ruleId-createruleTenantId-code1-message1:
          - ["创建同名台账规则","${ENV(ouid)}","${ENV(filekey1)}","孔明","甲方姓名","pers","姓名","杭州市西湖区","甲方姓名","locs","地址","","${ENV(orgid2)}",********,"台账字段不能重复"]
