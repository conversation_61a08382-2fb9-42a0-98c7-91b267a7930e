config:
    name: 台账规则开关设置

testcases:
-
    name: 台账规则开关设置-$CaseName
    testcase: testcases/standing_book/standing_book_switch_action.yml
    parameters:
      - CaseName-accountId-ruleTenantId-code1-message1-switchoid-enabled-switchTenantId-code2-message2-data2:
          - ["关闭台账开关","${ENV(ouid)}","${ENV(orgid2)}",0,"成功","${ENV(ouid)}",false,"${ENV(orgid2)}",0,"成功",true]
          - ["打开台账开关","${ENV(ouid)}","${ENV(orgid2)}",0,"成功","${ENV(ouid)}",true,"${ENV(orgid2)}",0,"成功",true]