config:
    name: 添加目录查看&下载&编辑权限，对合同进行编辑

testcases:
-
    name: 添加目录查看&下载&编辑权限，对合同进行编辑-$CaseName
    testcase: testcases/userprivilege/adduserprivilege_one.yml
    parameters:
      - CaseName-accountId1-name-parentMenuId-createTenantId-code1-message1-groupoid-processIdList-validTime-groupTenantId-code2-message2-ouidadduser-username-useroid-roleId-mobileNo-addTenantId-code3-message3-querylistoid-title-initiator-createFrom-createEnd-querylistTenantId-code4-message4-data-processId_UP-upoid-validTime2-updateTenantId-code5-message5-processId-removeoid-targetMenuId-removeTenantId-code6-message6-accountId2-deleteTenantId-code7-message7:
          - ["添加用户可查看权限","${ENV(ouid)}","see-${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}",['${ENV(processId_org1)}'],"","${ENV(orgid)}",0,"成功","${ENV(ouid)}","武玉华","${ENV(ouid2)}","2543756aea364646a64e6a4de063ab6d","***********","${ENV(orgid)}",0,"成功","${ENV(ouid)}","","","","","${ENV(orgid)}",0,"成功",1,"${ENV(processId_org1)}","${ENV(ouid2)}","${groupingNum()}","${ENV(orgid)}",********,"无操作权限","${ENV(processId_org1)}","${ENV(ouid)}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]
          - ["添加用户可下载权限","${ENV(ouid)}","down-${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}",['${ENV(processId_org1)}'],"","${ENV(orgid)}",0,"成功","${ENV(ouid)}","武玉华","${ENV(ouid2)}","583aff2b2eae48ebb19da363b7160ea5","***********","${ENV(orgid)}",0,"成功","${ENV(ouid)}","","","","","${ENV(orgid)}",0,"成功",1,"${ENV(processId_org1)}","${ENV(ouid2)}","${groupingNum()}","${ENV(orgid)}",********,"无操作权限","${ENV(processId_org1)}","${ENV(ouid)}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]
          - ["添加用户可编辑权限","${ENV(ouid)}","edit-${getTimeStamp()}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}",['${ENV(processId_org1)}'],"","${ENV(orgid)}",0,"成功","${ENV(ouid)}","武玉华","${ENV(ouid2)}","4d80868d4a1e4881b8237e702738a5b2","***********","${ENV(orgid)}",0,"成功","${ENV(ouid)}","","","","","${ENV(orgid)}",0,"成功",1,"${ENV(processId_org1)}","${ENV(ouid2)}","${groupingNum()}","${ENV(orgid)}",0,"成功","${ENV(processId_org1)}","${ENV(ouid)}","","${ENV(orgid)}",0,"成功","${ENV(ouid)}","${ENV(orgid)}",0,"成功"]



