config:
  name: 新增版本-参数校验

testcases:

  -
    name: 新增版本-$CaseName
    testcase: testcases/versionmanage_action/addVersion_action.yml
    parameters:
      - CaseName-version-versionType-versionNote-upgradable-code-message:
          - ["version为空","",0,"测试",false,20000002,"版本号不能为空"]
          - ["version格式不符合","123",0,"测试",false,20000104,"版本号规则不匹配"]
          - ["versionType为空","1.0.0",null,"测试",false,20000002,"版本类型不能为空"]
          - ["versionType传0、1以外的值","1.0.0",2,"测试",false,20000002,"版本类型无效"]
          - ["versionNote为空","1.0.0",0,"",false,20000002,"版本说明不能为空"]
